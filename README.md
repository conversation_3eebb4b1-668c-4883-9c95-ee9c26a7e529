# PyCRM - 客户关系管理系统

一个基于 Python FastAPI + React 的现代化CRM系统，支持自定义字段、模块化操作和API接口。

## 🚀 功能特性

### 核心功能
- ✅ **用户认证系统** - JWT token认证，支持登录/注册
- ✅ **客户管理** - 完整的客户信息管理，支持增删改查
- ✅ **自定义字段** - 灵活的字段配置，支持多种字段类型
- ✅ **数据统计** - 客户数据分析和可视化图表
- ✅ **响应式界面** - 基于Ant Design的现代化UI

### 技术特性
- 🔧 **模块化设计** - 前后端分离，易于扩展
- 🔧 **API优先** - 完整的RESTful API接口
- 🔧 **数据库支持** - SQLite(开发) → MySQL(生产)
- 🔧 **类型安全** - TypeScript前端 + Pydantic后端
- 🔧 **快速部署** - 简单的安装和配置流程

## 📋 系统要求

- Python 3.7+
- Node.js 14+
- npm 或 yarn

## 🛠️ 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd pycrm
```

### 2. 后端设置
```bash
# 安装Python依赖
pip install -r requirements.txt

# 启动后端服务器
python run.py
```
后端服务将在 http://localhost:8001 启动

### 3. 前端设置
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```
前端应用将在 http://localhost:5173 启动

./start.sh

# 后端
python run.py

# 前端
cd frontend && npm run dev

## 🔑 默认账号

- **用户名**: admin
- **密码**: admin

## 📚 使用指南

### 1. 登录系统
使用默认账号登录，或注册新用户账号。

### 2. 客户管理
- **查看客户列表**: 在客户管理页面查看所有客户
- **添加客户**: 点击"新建客户"按钮添加新客户
- **编辑客户**: 点击客户列表中的"编辑"按钮
- **查看详情**: 点击客户姓名或"查看"按钮进入详情页

### 3. 自定义字段配置
- **添加字段**: 在字段配置页面添加新的自定义字段
- **字段类型**: 支持文本、数字、邮箱、电话、日期、下拉选择等
- **字段验证**: 可设置必填字段和默认值

### 4. 数据分析
- **仪表板**: 查看客户统计数据和图表分析
- **客户等级分布**: 查看不同等级客户的分布情况
- **行业分析**: 了解客户的行业分布

## 🏗️ 项目结构

```
pycrm/
├── app/                    # 后端应用
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── crud/              # 数据库操作
│   ├── db/                # 数据库相关
│   ├── models/            # 数据模型
│   └── schemas/           # Pydantic模型
├── frontend/              # 前端应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── contexts/      # React上下文
│   │   ├── pages/         # 页面组件
│   │   └── services/      # API服务
│   └── public/
├── requirements.txt       # Python依赖
├── main.py               # FastAPI应用入口
├── run.py                # 简化启动脚本
└── README.md
```

## 🔧 配置说明

### 环境变量 (.env)
```env
# 数据库配置
DATABASE_URL=sqlite:///./crm.db
# DATABASE_URL=mysql+pymysql://username:password@localhost/crm_db

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=PyCRM
DEBUG=True
```

### 数据库迁移
系统首次启动时会自动创建数据库表和默认数据。

## 📖 API文档

启动后端服务后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8001/docs
- ReDoc: http://localhost:8001/redoc

### 主要API端点
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/customers` - 获取客户列表
- `POST /api/v1/customers` - 创建客户
- `GET /api/v1/customers/{id}` - 获取客户详情
- `PUT /api/v1/customers/{id}` - 更新客户
- `DELETE /api/v1/customers/{id}` - 删除客户
- `GET /api/v1/custom-fields` - 获取自定义字段
- `POST /api/v1/custom-fields` - 创建自定义字段

## 🚀 生产部署

### 数据库切换到MySQL
1. 安装MySQL数据库
2. 创建数据库: `CREATE DATABASE crm_db;`
3. 修改 `.env` 文件中的 `DATABASE_URL`
4. 重启应用

### 前端构建
```bash
cd frontend
npm run build
```

### 使用Docker部署
```bash
# 构建镜像
docker build -t pycrm .

# 运行容器
docker run -p 8001:8001 pycrm
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请提交 Issue 或联系开发团队。

---

**PyCRM** - 让客户关系管理更简单、更高效！
