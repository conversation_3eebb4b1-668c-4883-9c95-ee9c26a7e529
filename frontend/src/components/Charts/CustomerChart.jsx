import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Typography, Space } from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  TrophyOutlined,
  RiseOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const CustomerChart = ({ customers = [] }) => {
  const [stats, setStats] = useState({
    total: 0,
    byLevel: {},
    byIndustry: {},
    recentGrowth: 0,
  });

  useEffect(() => {
    calculateStats();
  }, [customers]);

  const calculateStats = () => {
    const total = customers.length;
    const byLevel = {};
    const byIndustry = {};

    customers.forEach(customer => {
      // 统计客户等级
      const level = customer.custom_fields?.customer_level || '未分类';
      byLevel[level] = (byLevel[level] || 0) + 1;

      // 统计行业分布
      const industry = customer.custom_fields?.industry || '未分类';
      byIndustry[industry] = (byIndustry[industry] || 0) + 1;
    });

    // 模拟增长率
    const recentGrowth = Math.floor(Math.random() * 30) + 10;

    setStats({
      total,
      byLevel,
      byIndustry,
      recentGrowth,
    });
  };

  const levelColors = {
    'VIP客户': '#f50',
    '重要客户': '#2db7f5',
    '普通客户': '#87d068',
    '潜在客户': '#108ee9',
    '未分类': '#d9d9d9',
  };

  const industryColors = {
    '互联网': '#1890ff',
    '金融': '#52c41a',
    '制造业': '#faad14',
    '教育': '#722ed1',
    '医疗': '#eb2f96',
    '零售': '#13c2c2',
    '其他': '#d9d9d9',
    '未分类': '#d9d9d9',
  };

  return (
    <Row gutter={[16, 16]}>
      {/* 总体统计 */}
      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="客户总数"
            value={stats.total}
            prefix={<TeamOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="本月增长"
            value={stats.recentGrowth}
            suffix="%"
            prefix={<RiseOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="VIP客户"
            value={stats.byLevel['VIP客户'] || 0}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>

      <Col xs={24} sm={12} lg={6}>
        <Card>
          <Statistic
            title="活跃客户"
            value={Math.floor(stats.total * 0.8)}
            prefix={<UserOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>

      {/* 客户等级分布 */}
      <Col xs={24} lg={12}>
        <Card title="客户等级分布" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {Object.entries(stats.byLevel).map(([level, count]) => (
              <div key={level}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>{level}</Text>
                  <Text strong>{count}人</Text>
                </div>
                <Progress
                  percent={stats.total > 0 ? Math.round((count / stats.total) * 100) : 0}
                  strokeColor={levelColors[level]}
                  size="small"
                />
              </div>
            ))}
          </Space>
        </Card>
      </Col>

      {/* 行业分布 */}
      <Col xs={24} lg={12}>
        <Card title="行业分布" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {Object.entries(stats.byIndustry).map(([industry, count]) => (
              <div key={industry}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <Text>{industry}</Text>
                  <Text strong>{count}人</Text>
                </div>
                <Progress
                  percent={stats.total > 0 ? Math.round((count / stats.total) * 100) : 0}
                  strokeColor={industryColors[industry]}
                  size="small"
                />
              </div>
            ))}
          </Space>
        </Card>
      </Col>

      {/* 客户价值分析 */}
      <Col xs={24}>
        <Card title="客户价值分析" size="small">
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                  {stats.byLevel['VIP客户'] || 0}
                </div>
                <div>高价值客户</div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                  {(stats.byLevel['重要客户'] || 0) + (stats.byLevel['VIP客户'] || 0)}
                </div>
                <div>核心客户</div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                  {stats.byLevel['潜在客户'] || 0}
                </div>
                <div>潜在客户</div>
              </div>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
  );
};

export default CustomerChart;
