import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Card,
  Typography,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Select,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { customerAPI, customFieldAPI } from '../services/api';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;
const { Search } = Input;

const Customers = () => {
  const [customers, setCustomers] = useState([]);
  const [customFields, setCustomFields] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadCustomers();
    loadCustomFields();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const response = await customerAPI.getCustomers();
      setCustomers(response.customers || []);
    } catch (error) {
      message.error('加载客户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomFields = async () => {
    try {
      const fields = await customFieldAPI.getFields('customer');
      setCustomFields(fields || []);
    } catch (error) {
      console.error('Failed to load custom fields:', error);
    }
  };

  const handleAdd = () => {
    setEditingCustomer(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingCustomer(record);
    form.setFieldsValue({
      ...record,
      ...record.custom_fields,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await customerAPI.deleteCustomer(id);
      message.success('删除成功');
      loadCustomers();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      const customFieldValues = {};
      const basicFields = ['name', 'email', 'phone', 'company', 'address', 'notes'];

      // 分离基本字段和自定义字段
      Object.keys(values).forEach(key => {
        if (!basicFields.includes(key)) {
          customFieldValues[key] = values[key];
          delete values[key];
        }
      });

      const customerData = {
        ...values,
        custom_fields: customFieldValues,
      };

      if (editingCustomer) {
        await customerAPI.updateCustomer(editingCustomer.id, customerData);
        message.success('更新成功');
      } else {
        await customerAPI.createCustomer(customerData);
        message.success('创建成功');
      }

      setModalVisible(false);
      loadCustomers();
    } catch (error) {
      message.error(editingCustomer ? '更新失败' : '创建失败');
    }
  };

  // 基础列定义
  const baseColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) =>
        record.name.toLowerCase().includes(value.toLowerCase()) ||
        (record.email && record.email.toLowerCase().includes(value.toLowerCase())) ||
        (record.company && record.company.toLowerCase().includes(value.toLowerCase())),
      render: (text, record) => (
        <Space>
          <UserOutlined />
          <Button
            type="link"
            onClick={() => navigate(`/customers/${record.id}`)}
            style={{ padding: 0, height: 'auto' }}
          >
            {text}
          </Button>
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      render: (text) => text ? <Tag color="blue">{text}</Tag> : '-',
    },
  ];

  // 动态添加自定义字段列
  const customFieldColumns = customFields.map(field => ({
    title: field.label,
    dataIndex: ['custom_fields', field.name],
    key: field.name,
    render: (value) => value || '-',
  }));

  const actionColumn = {
    title: '操作',
    key: 'action',
    render: (_, record) => (
      <Space size="middle">
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/customers/${record.id}`)}
        >
          查看
        </Button>
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>
        <Popconfirm
          title="确定要删除这个客户吗？"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" danger icon={<DeleteOutlined />}>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  };

  const columns = [...baseColumns, ...customFieldColumns, actionColumn];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>客户管理</Title>
          <Space>
            <Search
              placeholder="搜索客户"
              allowClear
              style={{ width: 200 }}
              onSearch={setSearchText}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新建客户
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={customers}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingCustomer ? '编辑客户' : '新建客户'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item name="phone" label="电话">
            <Input placeholder="请输入电话" />
          </Form.Item>

          <Form.Item name="company" label="公司">
            <Input placeholder="请输入公司名称" />
          </Form.Item>

          <Form.Item name="address" label="地址">
            <Input.TextArea placeholder="请输入地址" rows={2} />
          </Form.Item>

          {/* 动态渲染自定义字段 */}
          {customFields.map(field => (
            <Form.Item
              key={field.name}
              name={field.name}
              label={field.label}
              rules={field.is_required === 'true' ? [{ required: true, message: `请输入${field.label}` }] : []}
            >
              {field.field_type === 'textarea' ? (
                <Input.TextArea placeholder={`请输入${field.label}`} rows={2} />
              ) : field.field_type === 'select' && field.options ? (
                <Select placeholder={`请选择${field.label}`}>
                  {JSON.parse(field.options).map(option => (
                    <Select.Option key={option} value={option}>{option}</Select.Option>
                  ))}
                </Select>
              ) : (
                <Input
                  type={field.field_type === 'number' ? 'number' : 'text'}
                  placeholder={`请输入${field.label}`}
                />
              )}
            </Form.Item>
          ))}

          <Form.Item name="notes" label="备注">
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Customers;
