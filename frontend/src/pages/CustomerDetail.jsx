import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tabs,
  Table,
  Divider,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { customerAPI, customFieldAPI } from '../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const CustomerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(null);
  const [customFields, setCustomFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (id) {
      loadCustomer();
      loadCustomFields();
    }
  }, [id]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      const data = await customerAPI.getCustomer(id);
      setCustomer(data);
    } catch (error) {
      message.error('加载客户信息失败');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomFields = async () => {
    try {
      const fields = await customFieldAPI.getFields('customer');
      setCustomFields(fields || []);
    } catch (error) {
      console.error('Failed to load custom fields:', error);
    }
  };

  const handleEdit = () => {
    form.setFieldsValue({
      ...customer,
      ...customer.custom_fields,
    });
    setEditModalVisible(true);
  };

  const handleUpdate = async (values) => {
    try {
      const customFieldValues = {};
      const basicFields = ['name', 'email', 'phone', 'company', 'address', 'notes'];

      Object.keys(values).forEach(key => {
        if (!basicFields.includes(key)) {
          customFieldValues[key] = values[key];
          delete values[key];
        }
      });

      const customerData = {
        ...values,
        custom_fields: customFieldValues,
      };

      await customerAPI.updateCustomer(id, customerData);
      message.success('更新成功');
      setEditModalVisible(false);
      loadCustomer();
    } catch (error) {
      message.error('更新失败');
    }
  };

  const handleDelete = async () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个客户吗？此操作不可恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await customerAPI.deleteCustomer(id);
          message.success('删除成功');
          navigate('/customers');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const getFieldTypeIcon = (fieldType) => {
    switch (fieldType) {
      case 'email': return <MailOutlined />;
      case 'phone': return <PhoneOutlined />;
      case 'text': return <GlobalOutlined />;
      default: return null;
    }
  };

  if (loading) {
    return <Card loading={loading} />;
  }

  if (!customer) {
    return <Card>客户不存在</Card>;
  }

  // 联系人数据（模拟）
  const contactsData = [
    {
      key: '1',
      name: '联系人1',
      position: '技术总监',
      phone: '13800138001',
      email: '<EMAIL>',
    },
    {
      key: '2',
      name: '联系人2',
      position: '采购经理',
      phone: '13800138002',
      email: '<EMAIL>',
    },
  ];

  const contactColumns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '职位', dataIndex: 'position', key: 'position' },
    { title: '电话', dataIndex: 'phone', key: 'phone' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small" danger>删除</Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/customers')}
            >
              返回客户列表
            </Button>
            <Title level={3} style={{ margin: 0 }}>
              <UserOutlined /> {customer.name}
            </Title>
          </Space>
          <div style={{ float: 'right' }}>
            <Space>
              <Button icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
              <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
                删除
              </Button>
            </Space>
          </div>
        </div>

        <Tabs defaultActiveKey="basic">
          <TabPane tab="基本信息" key="basic">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="客户姓名" span={1}>
                <Text strong>{customer.name}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="公司名称" span={1}>
                {customer.company || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="邮箱" span={1}>
                <Space>
                  <MailOutlined />
                  {customer.email || '-'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="电话" span={1}>
                <Space>
                  <PhoneOutlined />
                  {customer.phone || '-'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="地址" span={2}>
                <Space>
                  <HomeOutlined />
                  {customer.address || '-'}
                </Space>
              </Descriptions.Item>

              {/* 动态显示自定义字段 */}
              {customFields.map(field => {
                const value = customer.custom_fields?.[field.name];
                if (!value) return null;

                return (
                  <Descriptions.Item label={field.label} key={field.name} span={1}>
                    <Space>
                      {getFieldTypeIcon(field.field_type)}
                      {field.field_type === 'select' ? (
                        <Tag color="blue">{value}</Tag>
                      ) : (
                        <Text>{value}</Text>
                      )}
                    </Space>
                  </Descriptions.Item>
                );
              })}

              <Descriptions.Item label="备注" span={2}>
                {customer.notes || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={1}>
                {new Date(customer.created_at).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间" span={1}>
                {customer.updated_at ? new Date(customer.updated_at).toLocaleString() : '-'}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="联系人" key="contacts">
            <div style={{ marginBottom: 16 }}>
              <Button type="primary">添加联系人</Button>
            </div>
            <Table
              columns={contactColumns}
              dataSource={contactsData}
              pagination={false}
              size="small"
            />
          </TabPane>

          <TabPane tab="销售机会" key="opportunities">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">暂无销售机会</Text>
              <br />
              <Button type="primary" style={{ marginTop: 16 }}>
                创建销售机会
              </Button>
            </div>
          </TabPane>

          <TabPane tab="活动记录" key="activities">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">暂无活动记录</Text>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑客户信息"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdate}
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item name="email" label="邮箱">
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item name="phone" label="电话">
            <Input placeholder="请输入电话" />
          </Form.Item>

          <Form.Item name="company" label="公司">
            <Input placeholder="请输入公司名称" />
          </Form.Item>

          <Form.Item name="address" label="地址">
            <Input.TextArea placeholder="请输入地址" rows={2} />
          </Form.Item>

          {/* 动态渲染自定义字段 */}
          {customFields.map(field => (
            <Form.Item
              key={field.name}
              name={field.name}
              label={field.label}
            >
              {field.field_type === 'textarea' ? (
                <Input.TextArea placeholder={`请输入${field.label}`} rows={2} />
              ) : field.field_type === 'select' && field.options ? (
                <Select placeholder={`请选择${field.label}`}>
                  {JSON.parse(field.options).map(option => (
                    <Select.Option key={option} value={option}>{option}</Select.Option>
                  ))}
                </Select>
              ) : (
                <Input
                  type={field.field_type === 'number' ? 'number' : 'text'}
                  placeholder={`请输入${field.label}`}
                />
              )}
            </Form.Item>
          ))}

          <Form.Item name="notes" label="备注">
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerDetail;
