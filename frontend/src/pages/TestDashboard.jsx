import React from 'react';
import { <PERSON>, Row, Col, Statistic, Typography, Button, Space } from 'antd';
import {
  TeamOutlined,
  UserAddOutlined,
  DollarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const TestDashboard = () => {
  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>🎯 测试仪表板</Title>
        <Paragraph type="secondary">
          这是一个简化的测试页面
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总客户数"
              value={10}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="新增客户"
              value={5}
              prefix={<UserAddOutlined />}
              suffix="本月"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={88888}
              prefix={<DollarOutlined />}
              precision={0}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃交易"
              value={15}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Space wrap>
          <Button type="primary" size="large">
            新建客户
          </Button>
          <Button size="large">
            客户管理
          </Button>
          <Button size="large">
            字段配置
          </Button>
        </Space>
      </Card>

      {/* 系统信息 */}
      <Card title="系统信息">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Paragraph>
              <strong>版本:</strong> 1.0.0
            </Paragraph>
            <Paragraph>
              <strong>数据库:</strong> SQLite
            </Paragraph>
          </Col>
          <Col span={12}>
            <Paragraph>
              <strong>后端:</strong> FastAPI + Python
            </Paragraph>
            <Paragraph>
              <strong>前端:</strong> React + Ant Design
            </Paragraph>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default TestDashboard;
