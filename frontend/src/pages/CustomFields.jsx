import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Card,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tag,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { customFieldAPI } from '../services/api';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CustomFields = () => {
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingField, setEditingField] = useState(null);
  const [form] = Form.useForm();

  const fieldTypes = [
    { value: 'text', label: '文本' },
    { value: 'number', label: '数字' },
    { value: 'email', label: '邮箱' },
    { value: 'phone', label: '电话' },
    { value: 'date', label: '日期' },
    { value: 'datetime', label: '日期时间' },
    { value: 'boolean', label: '布尔值' },
    { value: 'select', label: '下拉选择' },
    { value: 'textarea', label: '多行文本' },
  ];

  const entityTypes = [
    { value: 'customer', label: '客户' },
    { value: 'contact', label: '联系人' },
    { value: 'opportunity', label: '销售机会' },
  ];

  useEffect(() => {
    loadFields();
  }, []);

  const loadFields = async () => {
    try {
      setLoading(true);
      const customerFields = await customFieldAPI.getFields('customer');
      setFields(customerFields || []);
    } catch (error) {
      message.error('加载字段列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingField(null);
    form.resetFields();
    form.setFieldsValue({ entity_type: 'customer', is_required: false });
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingField(record);
    form.setFieldsValue({
      ...record,
      is_required: record.is_required === 'true',
      options: record.options ? JSON.parse(record.options).join('\n') : '',
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await customFieldAPI.deleteField(id);
      message.success('删除成功');
      loadFields();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      const fieldData = {
        ...values,
        is_required: values.is_required ? 'true' : 'false',
        options: values.field_type === 'select' && values.options
          ? JSON.stringify(values.options.split('\n').filter(opt => opt.trim()))
          : null,
      };

      if (editingField) {
        await customFieldAPI.updateField(editingField.id, fieldData);
        message.success('更新成功');
      } else {
        await customFieldAPI.createField(fieldData);
        message.success('创建成功');
      }

      setModalVisible(false);
      loadFields();
    } catch (error) {
      message.error(editingField ? '更新失败' : '创建失败');
    }
  };

  const columns = [
    {
      title: '字段名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <code>{text}</code>,
    },
    {
      title: '显示标签',
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: '字段类型',
      dataIndex: 'field_type',
      key: 'field_type',
      render: (type) => {
        const typeInfo = fieldTypes.find(t => t.value === type);
        return <Tag color="blue">{typeInfo?.label || type}</Tag>;
      },
    },
    {
      title: '实体类型',
      dataIndex: 'entity_type',
      key: 'entity_type',
      render: (type) => {
        const entityInfo = entityTypes.find(t => t.value === type);
        return <Tag color="green">{entityInfo?.label || type}</Tag>;
      },
    },
    {
      title: '必填',
      dataIndex: 'is_required',
      key: 'is_required',
      render: (required) => (
        <Tag color={required === 'true' ? 'red' : 'default'}>
          {required === 'true' ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '排序',
      dataIndex: 'order_index',
      key: 'order_index',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个字段吗？删除后相关数据将丢失！"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <SettingOutlined /> 自定义字段配置
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新建字段
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={fields}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 个字段`,
          }}
        />
      </Card>

      <Modal
        title={editingField ? '编辑字段' : '新建字段'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="字段名称"
            rules={[
              { required: true, message: '请输入字段名称' },
              { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名称只能包含字母、数字和下划线，且不能以数字开头' }
            ]}
          >
            <Input placeholder="例如: custom_field_1" />
          </Form.Item>

          <Form.Item
            name="label"
            label="显示标签"
            rules={[{ required: true, message: '请输入显示标签' }]}
          >
            <Input placeholder="例如: 客户等级" />
          </Form.Item>

          <Form.Item
            name="field_type"
            label="字段类型"
            rules={[{ required: true, message: '请选择字段类型' }]}
          >
            <Select placeholder="请选择字段类型">
              {fieldTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="entity_type"
            label="实体类型"
            rules={[{ required: true, message: '请选择实体类型' }]}
          >
            <Select placeholder="请选择实体类型">
              {entityTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.field_type !== currentValues.field_type
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('field_type') === 'select' ? (
                <Form.Item
                  name="options"
                  label="选项列表"
                  rules={[{ required: true, message: '请输入选项列表' }]}
                >
                  <TextArea
                    placeholder="每行一个选项，例如：&#10;VIP客户&#10;普通客户&#10;潜在客户"
                    rows={4}
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item name="default_value" label="默认值">
            <Input placeholder="请输入默认值（可选）" />
          </Form.Item>

          <Form.Item name="order_index" label="排序序号">
            <InputNumber min={0} placeholder="数字越小越靠前" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="is_required" label="是否必填" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomFields;
