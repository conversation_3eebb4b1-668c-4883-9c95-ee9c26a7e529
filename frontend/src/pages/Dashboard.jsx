import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button, Divider } from 'antd';
import {
  TeamOutlined,
  UserAddOutlined,
  DollarOutlined,
  TrophyOutlined,
  PlusOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { customerAPI } from '../services/api';
import CustomerChart from '../components/Charts/CustomerChart';

const { Title, Paragraph } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    newCustomers: 0,
    totalRevenue: 0,
    activeDeals: 0,
  });
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await customerAPI.getCustomers();
      setCustomers(response.customers || []);
      setStats({
        totalCustomers: response.total || 0,
        newCustomers: Math.floor(Math.random() * 10) + 1, // 模拟数据
        totalRevenue: Math.floor(Math.random() * 100000) + 50000, // 模拟数据
        activeDeals: Math.floor(Math.random() * 20) + 5, // 模拟数据
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: '新建客户',
      description: '添加新的客户信息',
      icon: <UserAddOutlined />,
      action: () => navigate('/customers/new'),
      color: '#1890ff',
    },
    {
      title: '客户管理',
      description: '查看和管理所有客户',
      icon: <TeamOutlined />,
      action: () => navigate('/customers'),
      color: '#52c41a',
    },
    {
      title: '字段配置',
      description: '自定义客户字段',
      icon: <PlusOutlined />,
      action: () => navigate('/custom-fields'),
      color: '#722ed1',
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>仪表板</Title>
        <Paragraph type="secondary">
          欢迎使用 PyCRM 客户关系管理系统
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总客户数"
              value={stats.totalCustomers}
              prefix={<TeamOutlined />}
              loading={loading}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="新增客户"
              value={stats.newCustomers}
              prefix={<UserAddOutlined />}
              suffix="本月"
              loading={loading}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={stats.totalRevenue}
              prefix={<DollarOutlined />}
              precision={0}
              loading={loading}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃交易"
              value={stats.activeDeals}
              prefix={<TrophyOutlined />}
              loading={loading}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card
                hoverable
                onClick={action.action}
                style={{
                  textAlign: 'center',
                  borderColor: action.color,
                  cursor: 'pointer',
                }}
              >
                <Space direction="vertical" size="middle">
                  <div style={{ fontSize: 32, color: action.color }}>
                    {action.icon}
                  </div>
                  <div>
                    <Title level={4} style={{ margin: 0 }}>
                      {action.title}
                    </Title>
                    <Paragraph type="secondary" style={{ margin: 0 }}>
                      {action.description}
                    </Paragraph>
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 客户数据分析 */}
      <Card title={<><BarChartOutlined /> 客户数据分析</>} style={{ marginBottom: 24 }}>
        <CustomerChart customers={customers} />
      </Card>

      {/* 系统信息 */}
      <Card title="系统信息">
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Paragraph>
              <strong>版本:</strong> 1.0.0
            </Paragraph>
            <Paragraph>
              <strong>数据库:</strong> SQLite
            </Paragraph>
          </Col>
          <Col span={12}>
            <Paragraph>
              <strong>后端:</strong> FastAPI + Python
            </Paragraph>
            <Paragraph>
              <strong>前端:</strong> React + Ant Design
            </Paragraph>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Dashboard;
