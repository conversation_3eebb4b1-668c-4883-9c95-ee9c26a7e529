import * as React from 'react';
import type { GetIndicatorSize } from './hooks/useIndicator';
import type { AnimatedConfig, EditableConfig, MoreProps, OnTabScroll, RenderTabBar, Tab, TabBarExtraContent, TabPosition, TabsLocale } from './interface';
export interface TabsProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange' | 'children'> {
    prefixCls?: string;
    className?: string;
    style?: React.CSSProperties;
    id?: string;
    items?: Tab[];
    activeKey?: string;
    defaultActiveKey?: string;
    direction?: 'ltr' | 'rtl';
    animated?: boolean | AnimatedConfig;
    renderTabBar?: RenderTabBar;
    tabBarExtraContent?: TabBarExtraContent;
    tabBarGutter?: number;
    tabBarStyle?: React.CSSProperties;
    tabPosition?: TabPosition;
    destroyInactiveTabPane?: boolean;
    onChange?: (activeKey: string) => void;
    onTabClick?: (activeKey: string, e: React.KeyboardEvent | React.MouseEvent) => void;
    onTabScroll?: OnTabScroll;
    editable?: EditableConfig;
    getPopupContainer?: (node: HTMLElement) => HTMLElement;
    locale?: TabsLocale;
    more?: MoreProps;
    /** @private Internal usage. Not promise will rename in future */
    popupClassName?: string;
    indicator?: {
        size?: GetIndicatorSize;
        align?: 'start' | 'center' | 'end';
    };
}
declare const Tabs: React.ForwardRefExoticComponent<TabsProps & React.RefAttributes<HTMLDivElement>>;
export default Tabs;
