"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _context = require("@rc-component/context");
var _classnames = _interopRequireDefault(require("classnames"));
var _addEventListener = _interopRequireDefault(require("rc-util/lib/Dom/addEventListener"));
var _getScrollBarSize = _interopRequireDefault(require("rc-util/lib/getScrollBarSize"));
var React = _interopRequireWildcard(require("react"));
var _TableContext = _interopRequireDefault(require("./context/TableContext"));
var _useFrame = require("./hooks/useFrame");
var _raf = _interopRequireDefault(require("rc-util/lib/raf"));
var _offsetUtil = require("./utils/offsetUtil");
var _findDOMNode = require("rc-util/lib/Dom/findDOMNode");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var StickyScrollBar = function StickyScrollBar(_ref, ref) {
  var _scrollBodyRef$curren, _scrollBodyRef$curren2;
  var scrollBodyRef = _ref.scrollBodyRef,
    onScroll = _ref.onScroll,
    offsetScroll = _ref.offsetScroll,
    container = _ref.container,
    direction = _ref.direction;
  var prefixCls = (0, _context.useContext)(_TableContext.default, 'prefixCls');
  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;
  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;
  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);
  var scrollBarRef = React.useRef();
  var _useLayoutState = (0, _useFrame.useLayoutState)({
      scrollLeft: 0,
      isHiddenScrollBar: true
    }),
    _useLayoutState2 = (0, _slicedToArray2.default)(_useLayoutState, 2),
    scrollState = _useLayoutState2[0],
    setScrollState = _useLayoutState2[1];
  var refState = React.useRef({
    delta: 0,
    x: 0
  });
  var _React$useState = React.useState(false),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    isActive = _React$useState2[0],
    setActive = _React$useState2[1];
  var rafRef = React.useRef(null);
  React.useEffect(function () {
    return function () {
      _raf.default.cancel(rafRef.current);
    };
  }, []);
  var onMouseUp = function onMouseUp() {
    setActive(false);
  };
  var onMouseDown = function onMouseDown(event) {
    event.persist();
    refState.current.delta = event.pageX - scrollState.scrollLeft;
    refState.current.x = 0;
    setActive(true);
    event.preventDefault();
  };
  var onMouseMove = function onMouseMove(event) {
    var _window;
    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons
    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),
      buttons = _ref2.buttons;
    if (!isActive || buttons === 0) {
      // If out body mouse up, we can set isActive false when mouse move
      if (isActive) {
        setActive(false);
      }
      return;
    }
    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;
    var isRTL = direction === 'rtl';
    // Limit scroll range
    left = Math.max(isRTL ? scrollBarWidth - bodyWidth : 0, Math.min(isRTL ? 0 : bodyWidth - scrollBarWidth, left));
    // Calculate the scroll position and update
    var shouldScroll = !isRTL || Math.abs(left) + Math.abs(scrollBarWidth) < bodyWidth;
    if (shouldScroll) {
      onScroll({
        scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)
      });
      refState.current.x = event.pageX;
    }
  };
  var checkScrollBarVisible = function checkScrollBarVisible() {
    _raf.default.cancel(rafRef.current);
    rafRef.current = (0, _raf.default)(function () {
      if (!scrollBodyRef.current) {
        return;
      }
      var tableOffsetTop = (0, _offsetUtil.getOffset)(scrollBodyRef.current).top;
      var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;
      var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : (0, _offsetUtil.getOffset)(container).top + container.clientHeight;
      if (tableBottomOffset - (0, _getScrollBarSize.default)() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {
        setScrollState(function (state) {
          return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state), {}, {
            isHiddenScrollBar: true
          });
        });
      } else {
        setScrollState(function (state) {
          return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state), {}, {
            isHiddenScrollBar: false
          });
        });
      }
    });
  };
  var setScrollLeft = function setScrollLeft(left) {
    setScrollState(function (state) {
      return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state), {}, {
        scrollLeft: left / bodyScrollWidth * bodyWidth || 0
      });
    });
  };
  React.useImperativeHandle(ref, function () {
    return {
      setScrollLeft: setScrollLeft,
      checkScrollBarVisible: checkScrollBarVisible
    };
  });
  React.useEffect(function () {
    var onMouseUpListener = (0, _addEventListener.default)(document.body, 'mouseup', onMouseUp, false);
    var onMouseMoveListener = (0, _addEventListener.default)(document.body, 'mousemove', onMouseMove, false);
    checkScrollBarVisible();
    return function () {
      onMouseUpListener.remove();
      onMouseMoveListener.remove();
    };
  }, [scrollBarWidth, isActive]);

  // Loop for scroll event check
  React.useEffect(function () {
    if (!scrollBodyRef.current) return;
    var scrollParents = [];
    var parent = (0, _findDOMNode.getDOM)(scrollBodyRef.current);
    while (parent) {
      scrollParents.push(parent);
      parent = parent.parentElement;
    }
    scrollParents.forEach(function (p) {
      return p.addEventListener('scroll', checkScrollBarVisible, false);
    });
    window.addEventListener('resize', checkScrollBarVisible, false);
    window.addEventListener('scroll', checkScrollBarVisible, false);
    container.addEventListener('scroll', checkScrollBarVisible, false);
    return function () {
      scrollParents.forEach(function (p) {
        return p.removeEventListener('scroll', checkScrollBarVisible);
      });
      window.removeEventListener('resize', checkScrollBarVisible);
      window.removeEventListener('scroll', checkScrollBarVisible);
      container.removeEventListener('scroll', checkScrollBarVisible);
    };
  }, [container]);
  React.useEffect(function () {
    if (!scrollState.isHiddenScrollBar) {
      setScrollState(function (state) {
        var bodyNode = scrollBodyRef.current;
        if (!bodyNode) {
          return state;
        }
        return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state), {}, {
          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth
        });
      });
    }
  }, [scrollState.isHiddenScrollBar]);
  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {
    return null;
  }
  return /*#__PURE__*/React.createElement("div", {
    style: {
      height: (0, _getScrollBarSize.default)(),
      width: bodyWidth,
      bottom: offsetScroll
    },
    className: "".concat(prefixCls, "-sticky-scroll")
  }, /*#__PURE__*/React.createElement("div", {
    onMouseDown: onMouseDown,
    ref: scrollBarRef,
    className: (0, _classnames.default)("".concat(prefixCls, "-sticky-scroll-bar"), (0, _defineProperty2.default)({}, "".concat(prefixCls, "-sticky-scroll-bar-active"), isActive)),
    style: {
      width: "".concat(scrollBarWidth, "px"),
      transform: "translate3d(".concat(scrollState.scrollLeft, "px, 0, 0)")
    }
  }));
};
var _default = exports.default = /*#__PURE__*/React.forwardRef(StickyScrollBar);