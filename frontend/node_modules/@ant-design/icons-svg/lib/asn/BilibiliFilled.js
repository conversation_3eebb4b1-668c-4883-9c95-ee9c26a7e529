"use strict";
// This icon file is generated automatically.
Object.defineProperty(exports, "__esModule", { value: true });
var BilibiliFilled = { "icon": { "tag": "svg", "attrs": { "fill-rule": "evenodd", "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M310.13 596.45c-8-4.46-16.5-8.43-25-11.9a273.55 273.55 0 00-26.99-7.44c-2.5-.99-2.5 1-2.5 1.49 0 7.93.5 18.84 1.5 27.77 1 7.44 2 15.37 4 22.8 0 .5 0 1 .5 1.5 1 .99 2 1.48 3 .49 8-4.46 16-8.43 23-13.39 7.5-5.45 15.5-11.9 22-18.35 1.5-1.48 0-2.47.5-2.97m323.95-11.9a273.55 273.55 0 00-27-7.44c-2.5-.99-2.5 1-2.5 1.49 0 7.93.5 18.84 1.5 27.77 1 7.43 2 15.37 4 22.8 0 .5 0 1 .5 1.5 1 .99 2 1.48 3 .49 8-4.46 16-8.43 23-13.39 7.5-5.45 15.5-11.9 22-18.35 2-1.48.5-2.47.5-2.97-7.5-4.46-16.5-8.43-25-11.9" } }, { "tag": "path", "attrs": { "d": "M741.5 112H283c-94.5 0-171 76.5-171 171.5v458c.5 94 77 170.5 171 170.5h458c94.5 0 171-76.5 171-170.5v-458c.5-95-76-171.5-170.5-171.5m95 343.5H852v48h-15.5zM741 454l2 43-13.5 1.5-5-44.5zm-23.5 0l4 45.5L707 501l-6.5-47.5h17zM487 455.5h15v48h-15zm-96-1.5l2 43-13.5 1.5-5-44.5zm-23.5 0l4 45.5-14.5 2-6-47.5zM364 603c-20.5 65.5-148 59.5-159.5 57.5-9-161.5-23-196.5-34.5-275.5l54.5-22.5c1 71.5 9 185 9 185s108.5-15.5 132 47c.5 3 0 6-1.5 8.5m20.5 35.5l-23.5-124h35.5l13 123zm44.5-8l-27-235 33.5-1.5 21 236H429zm34-175h17.5v48H467zm41 190h-26.5l-9.5-126h36zm210-43C693.5 668 566 662 554.5 660c-9-161-23-196-34.5-275l54.5-22.5c1 71.5 9 185 9 185S692 532 715.5 594c.5 3 0 6-1.5 8.5m19.5 36l-23-124H746l13 123zm45.5-8l-27.5-235L785 394l21 236h-27zm33.5-175H830v48h-13zm41 190H827l-9.5-126h36z" } }] }, "name": "bilibili", "theme": "filled" };
exports.default = BilibiliFilled;
