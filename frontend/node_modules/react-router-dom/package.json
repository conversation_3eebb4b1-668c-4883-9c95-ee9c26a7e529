{"name": "react-router-dom", "version": "6.30.1", "description": "Declarative routing for React web applications", "keywords": ["react", "router", "route", "routing", "history", "link"], "repository": {"type": "git", "url": "https://github.com/remix-run/react-router", "directory": "packages/react-router-dom"}, "license": "MIT", "author": "Remix Software <<EMAIL>>", "sideEffects": false, "main": "./dist/main.js", "unpkg": "./dist/umd/react-router-dom.production.min.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"@remix-run/router": "1.23.0", "react-router": "6.30.1"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md", "server.d.ts", "server.js", "server.mjs"], "engines": {"node": ">=14.0.0"}}