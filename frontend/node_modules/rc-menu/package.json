{"name": "rc-menu", "version": "9.16.1", "description": "menu ui component for react", "keywords": ["react", "react-component", "menu", "ui", "react-menu"], "homepage": "http://github.com/react-component/menu", "bugs": {"url": "http://github.com/react-component/menu/issues"}, "repository": {"type": "git", "url": "**************:react-component/menu.git"}, "license": "MIT", "maintainers": ["<EMAIL>", "<EMAIL>"], "main": "./lib/index", "module": "./es/index", "files": ["es", "lib", "assets/*.css", "assets/*.less"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "lint": "eslint src/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "postpublish": "tnpm sync rc-menu", "start": "dumi dev", "test": "rc-test", "prepare": "husky && dumi setup"}, "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.0.0", "classnames": "2.x", "rc-motion": "^2.4.3", "rc-overflow": "^1.3.1", "rc-util": "^5.27.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^15.0.7", "@types/jest": "^29.5.2", "@types/node": "^22.3.0", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/warning": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^2.1.17", "eslint": "^8.55.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^51.0.1", "father": "^4.0.0", "gh-pages": "^6.1.0", "husky": "^9.1.6", "less": "^4.1.3", "lint-staged": "^15.2.10", "np": "^10.0.5", "prettier": "^3.3.3", "rc-test": "^7.0.14", "react": "^18.3.1", "react-dom": "^18.3.1", "regenerator-runtime": "^0.14.0", "typescript": "^5.1.6"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}}