export declare const defaultValidateMessages: {
    default: string;
    required: string;
    enum: string;
    whitespace: string;
    date: {
        format: string;
        parse: string;
        invalid: string;
    };
    types: {
        string: string;
        method: string;
        array: string;
        object: string;
        number: string;
        date: string;
        boolean: string;
        integer: string;
        float: string;
        regexp: string;
        email: string;
        url: string;
        hex: string;
    };
    string: {
        len: string;
        min: string;
        max: string;
        range: string;
    };
    number: {
        len: string;
        min: string;
        max: string;
        range: string;
    };
    array: {
        len: string;
        min: string;
        max: string;
        range: string;
    };
    pattern: {
        mismatch: string;
    };
};
