"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useInvalidate;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _rcUtil = require("rc-util");
/**
 * Check if provided date is valid for the `disabledDate` & `showTime.disabledTime`.
 */
function useInvalidate(generateConfig, picker, disabledDate, showTime) {
  // Check disabled date
  var isInvalidate = (0, _rcUtil.useEvent)(function (date, info) {
    var outsideInfo = (0, _objectSpread2.default)({
      type: picker
    }, info);
    delete outsideInfo.activeIndex;
    if (
    // Date object is invalid
    !generateConfig.isValidate(date) ||
    // Date is disabled by `disabledDate`
    disabledDate && disabledDate(date, outsideInfo)) {
      return true;
    }
    if ((picker === 'date' || picker === 'time') && showTime) {
      var _showTime$disabledTim;
      var range = info && info.activeIndex === 1 ? 'end' : 'start';
      var _ref = ((_showTime$disabledTim = showTime.disabledTime) === null || _showTime$disabledTim === void 0 ? void 0 : _showTime$disabledTim.call(showTime, date, range, {
          from: outsideInfo.from
        })) || {},
        disabledHours = _ref.disabledHours,
        disabledMinutes = _ref.disabledMinutes,
        disabledSeconds = _ref.disabledSeconds,
        disabledMilliseconds = _ref.disabledMilliseconds;
      var legacyDisabledHours = showTime.disabledHours,
        legacyDisabledMinutes = showTime.disabledMinutes,
        legacyDisabledSeconds = showTime.disabledSeconds;
      var mergedDisabledHours = disabledHours || legacyDisabledHours;
      var mergedDisabledMinutes = disabledMinutes || legacyDisabledMinutes;
      var mergedDisabledSeconds = disabledSeconds || legacyDisabledSeconds;
      var hour = generateConfig.getHour(date);
      var minute = generateConfig.getMinute(date);
      var second = generateConfig.getSecond(date);
      var millisecond = generateConfig.getMillisecond(date);
      if (mergedDisabledHours && mergedDisabledHours().includes(hour)) {
        return true;
      }
      if (mergedDisabledMinutes && mergedDisabledMinutes(hour).includes(minute)) {
        return true;
      }
      if (mergedDisabledSeconds && mergedDisabledSeconds(hour, minute).includes(second)) {
        return true;
      }
      if (disabledMilliseconds && disabledMilliseconds(hour, minute, second).includes(millisecond)) {
        return true;
      }
    }
    return false;
  });
  return isInvalidate;
}