"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classnames = _interopRequireDefault(require("classnames"));
var React = _interopRequireWildcard(require("react"));
var _dateUtil = require("../../../utils/dateUtil");
var _context = _interopRequireDefault(require("../../context"));
var _Icon = _interopRequireWildcard(require("../Icon"));
var _Input = _interopRequireDefault(require("../Input"));
var _useInputProps3 = _interopRequireDefault(require("../hooks/useInputProps"));
var _useRootProps = _interopRequireDefault(require("../hooks/useRootProps"));
var _MultipleDates = _interopRequireDefault(require("./MultipleDates"));
var _excluded = ["id", "open", "prefix", "clearIcon", "suffixIcon", "activeHelp", "allHelp", "focused", "onFocus", "onBlur", "onKeyDown", "locale", "generateConfig", "placeholder", "className", "style", "onClick", "onClear", "internalPicker", "value", "onChange", "onSubmit", "onInputChange", "multiple", "maxTagCount", "format", "maskFormat", "preserveInvalidOnBlur", "onInvalid", "disabled", "invalid", "inputReadOnly", "direction", "onOpenChange", "onMouseDown", "required", "aria-required", "autoFocus", "tabIndex", "removeIcon"];
function SingleSelector(props, ref) {
  var id = props.id,
    open = props.open,
    prefix = props.prefix,
    clearIcon = props.clearIcon,
    suffixIcon = props.suffixIcon,
    activeHelp = props.activeHelp,
    allHelp = props.allHelp,
    focused = props.focused,
    onFocus = props.onFocus,
    onBlur = props.onBlur,
    onKeyDown = props.onKeyDown,
    locale = props.locale,
    generateConfig = props.generateConfig,
    placeholder = props.placeholder,
    className = props.className,
    style = props.style,
    onClick = props.onClick,
    onClear = props.onClear,
    internalPicker = props.internalPicker,
    value = props.value,
    onChange = props.onChange,
    onSubmit = props.onSubmit,
    onInputChange = props.onInputChange,
    multiple = props.multiple,
    maxTagCount = props.maxTagCount,
    format = props.format,
    maskFormat = props.maskFormat,
    preserveInvalidOnBlur = props.preserveInvalidOnBlur,
    onInvalid = props.onInvalid,
    disabled = props.disabled,
    invalid = props.invalid,
    inputReadOnly = props.inputReadOnly,
    direction = props.direction,
    onOpenChange = props.onOpenChange,
    _onMouseDown = props.onMouseDown,
    required = props.required,
    ariaRequired = props['aria-required'],
    autoFocus = props.autoFocus,
    tabIndex = props.tabIndex,
    removeIcon = props.removeIcon,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var rtl = direction === 'rtl';

  // ======================== Prefix ========================
  var _React$useContext = React.useContext(_context.default),
    prefixCls = _React$useContext.prefixCls;

  // ========================= Refs =========================
  var rootRef = React.useRef();
  var inputRef = React.useRef();
  React.useImperativeHandle(ref, function () {
    return {
      nativeElement: rootRef.current,
      focus: function focus(options) {
        var _inputRef$current;
        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.focus(options);
      },
      blur: function blur() {
        var _inputRef$current2;
        (_inputRef$current2 = inputRef.current) === null || _inputRef$current2 === void 0 || _inputRef$current2.blur();
      }
    };
  });

  // ======================== Props =========================
  var rootProps = (0, _useRootProps.default)(restProps);

  // ======================== Change ========================
  var onSingleChange = function onSingleChange(date) {
    onChange([date]);
  };
  var onMultipleRemove = function onMultipleRemove(date) {
    var nextValues = value.filter(function (oriDate) {
      return oriDate && !(0, _dateUtil.isSame)(generateConfig, locale, oriDate, date, internalPicker);
    });
    onChange(nextValues);

    // When `open`, it means user is operating the
    if (!open) {
      onSubmit();
    }
  };

  // ======================== Inputs ========================
  var _useInputProps = (0, _useInputProps3.default)((0, _objectSpread2.default)((0, _objectSpread2.default)({}, props), {}, {
      onChange: onSingleChange
    }), function (_ref) {
      var valueTexts = _ref.valueTexts;
      return {
        value: valueTexts[0] || '',
        active: focused
      };
    }),
    _useInputProps2 = (0, _slicedToArray2.default)(_useInputProps, 2),
    getInputProps = _useInputProps2[0],
    getText = _useInputProps2[1];

  // ======================== Clear =========================
  var showClear = !!(clearIcon && value.length && !disabled);

  // ======================= Multiple =======================
  var selectorNode = multiple ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(_MultipleDates.default, {
    prefixCls: prefixCls,
    value: value,
    onRemove: onMultipleRemove,
    formatDate: getText,
    maxTagCount: maxTagCount,
    disabled: disabled,
    removeIcon: removeIcon,
    placeholder: placeholder
  }), /*#__PURE__*/React.createElement("input", {
    className: "".concat(prefixCls, "-multiple-input"),
    value: value.map(getText).join(','),
    ref: inputRef,
    readOnly: true,
    autoFocus: autoFocus,
    tabIndex: tabIndex
  }), /*#__PURE__*/React.createElement(_Icon.default, {
    type: "suffix",
    icon: suffixIcon
  }), showClear && /*#__PURE__*/React.createElement(_Icon.ClearIcon, {
    icon: clearIcon,
    onClear: onClear
  })) : /*#__PURE__*/React.createElement(_Input.default, (0, _extends2.default)({
    ref: inputRef
  }, getInputProps(), {
    autoFocus: autoFocus,
    tabIndex: tabIndex,
    suffixIcon: suffixIcon,
    clearIcon: showClear && /*#__PURE__*/React.createElement(_Icon.ClearIcon, {
      icon: clearIcon,
      onClear: onClear
    }),
    showActiveCls: false
  }));

  // ======================== Render ========================
  return /*#__PURE__*/React.createElement("div", (0, _extends2.default)({}, rootProps, {
    className: (0, _classnames.default)(prefixCls, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, "".concat(prefixCls, "-multiple"), multiple), "".concat(prefixCls, "-focused"), focused), "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-invalid"), invalid), "".concat(prefixCls, "-rtl"), rtl), className),
    style: style,
    ref: rootRef,
    onClick: onClick
    // Not lose current input focus
    ,
    onMouseDown: function onMouseDown(e) {
      var _inputRef$current3;
      var target = e.target;
      if (target !== ((_inputRef$current3 = inputRef.current) === null || _inputRef$current3 === void 0 ? void 0 : _inputRef$current3.inputElement)) {
        e.preventDefault();
      }
      _onMouseDown === null || _onMouseDown === void 0 || _onMouseDown(e);
    }
  }), prefix && /*#__PURE__*/React.createElement("div", {
    className: "".concat(prefixCls, "-prefix")
  }, prefix), selectorNode);
}
var RefSingleSelector = /*#__PURE__*/React.forwardRef(SingleSelector);
if (process.env.NODE_ENV !== 'production') {
  RefSingleSelector.displayName = 'SingleSelector';
}
var _default = exports.default = RefSingleSelector;