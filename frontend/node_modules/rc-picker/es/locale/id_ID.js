import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'id_ID',
  today: 'Hari ini',
  now: 'Sekarang',
  backToToday: 'Ke<PERSON>li ke hari ini',
  ok: 'Baik',
  clear: 'Be<PERSON>ih',
  week: '<PERSON><PERSON>',
  month: 'Bulan',
  year: '<PERSON><PERSON>',
  timeSelect: 'pilih waktu',
  dateSelect: 'pilih tanggal',
  weekSelect: 'Pilih satu minggu',
  monthSelect: 'Pilih satu bulan',
  yearSelect: 'Pilih satu tahun',
  decadeSelect: 'Pilih satu dekade',
  dateFormat: 'D/M/YYYY',
  dateTimeFormat: 'D/M/YYYY HH:mm:ss',
  previousMonth: '<PERSON><PERSON><PERSON> sebelumnya (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON> selan<PERSON> (PageDown)',
  previousYear: '<PERSON><PERSON> lalu (Control + kiri)',
  nextYear: '<PERSON>hun selanjutnya (Kontrol + kanan)',
  previousDecade: 'Dekade terakhir',
  nextDecade: 'Dekade berikutnya',
  previousCentury: 'Abad terakhir',
  nextCentury: 'Abad berikutnya'
});
export default locale;