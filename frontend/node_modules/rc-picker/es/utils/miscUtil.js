import _toConsumableArray from "@babel/runtime/helpers/esm/toConsumableArray";
export function leftPad(str, length) {
  var fill = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '0';
  var current = String(str);
  while (current.length < length) {
    current = "".concat(fill).concat(current);
  }
  return current;
}

/**
 * Convert `value` to array. Will provide `[]` if is null or undefined.
 */
export function toArray(val) {
  if (val === null || val === undefined) {
    return [];
  }
  return Array.isArray(val) ? val : [val];
}
export function fillIndex(ori, index, value) {
  var clone = _toConsumableArray(ori);
  clone[index] = value;
  return clone;
}

/** Pick props from the key list. Will filter empty value */
export function pickProps(props, keys) {
  var clone = {};
  var mergedKeys = keys || Object.keys(props);
  mergedKeys.forEach(function (key) {
    if (props[key] !== undefined) {
      clone[key] = props[key];
    }
  });
  return clone;
}
export function getRowFormat(picker, locale, format) {
  if (format) {
    return format;
  }
  switch (picker) {
    // All from the `locale.fieldXXXFormat` first
    case 'time':
      return locale.fieldTimeFormat;
    case 'datetime':
      return locale.fieldDateTimeFormat;
    case 'month':
      return locale.fieldMonthFormat;
    case 'year':
      return locale.fieldYearFormat;
    case 'quarter':
      return locale.fieldQuarterFormat;
    case 'week':
      return locale.fieldWeekFormat;
    default:
      return locale.fieldDateFormat;
  }
}
export function getFromDate(calendarValues, activeIndexList, activeIndex) {
  var mergedActiveIndex = activeIndex !== undefined ? activeIndex : activeIndexList[activeIndexList.length - 1];
  var firstValuedIndex = activeIndexList.find(function (index) {
    return calendarValues[index];
  });
  return mergedActiveIndex !== firstValuedIndex ? calendarValues[firstValuedIndex] : undefined;
}