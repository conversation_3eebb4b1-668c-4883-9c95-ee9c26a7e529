import type { InternalMode, Locale, SharedPickerProps } from '../interface';
export declare function leftPad(str: string | number, length: number, fill?: string): string;
/**
 * Convert `value` to array. Will provide `[]` if is null or undefined.
 */
export declare function toArray<T>(val: T | T[]): T[];
export declare function fillIndex<T extends any[]>(ori: T, index: number, value: T[number]): T;
/** Pick props from the key list. Will filter empty value */
export declare function pickProps<T extends object>(props: T, keys?: (keyof T)[] | readonly (keyof T)[]): T;
export declare function getRowFormat(picker: InternalMode, locale: Locale, format?: SharedPickerProps['format']): {
    format: string;
    type?: "mask";
} | import("../interface").FormatType<any> | import("../interface").FormatType<any>[];
export declare function getFromDate<DateType>(calendarValues: DateType[], activeIndexList: number[], activeIndex?: number): DateType;
