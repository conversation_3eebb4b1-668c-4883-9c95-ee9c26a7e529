#!/bin/bash

echo "🚀 启动 PyCRM 客户关系管理系统"
echo "=================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装 Python 3.7+"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 14+"
    exit 1
fi

echo "✅ 环境检查通过"

# 安装后端依赖
echo "📦 安装后端依赖..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
npm install

if [ $? -ne 0 ]; then
    echo "❌ 前端依赖安装失败"
    exit 1
fi

cd ..

echo "✅ 依赖安装完成"

# 启动后端服务器
echo "🔧 启动后端服务器..."
python run.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:8001/ > /dev/null; then
    echo "✅ 后端服务器启动成功 (http://localhost:8001)"
else
    echo "❌ 后端服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务器
echo "🔧 启动前端服务器..."
cd frontend
npm run dev &
FRONTEND_PID=$!

cd ..

echo ""
echo "🎉 PyCRM 系统启动成功！"
echo "=================================="
echo "📱 前端地址: http://localhost:5173"
echo "🔧 后端API: http://localhost:8001"
echo "📚 API文档: http://localhost:8001/docs"
echo ""
echo "🔑 默认登录账号:"
echo "   用户名: admin"
echo "   密码: admin"
echo ""
echo "按 Ctrl+C 停止服务器"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务器..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait
