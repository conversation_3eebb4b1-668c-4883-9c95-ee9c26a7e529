#!/usr/bin/env python3

import sys
import os
import webbrowser
import time
from threading import Timer

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8001')

def main():
    print("🚀 启动 PyCRM 客户关系管理系统")
    print("=" * 50)
    
    try:
        # 导入主应用
        from main import app
        print("✅ 应用加载成功")
        
        # 设置定时器打开浏览器
        timer = Timer(2.0, open_browser)
        timer.start()
        
        # 启动服务器
        import uvicorn
        print("🌐 启动服务器...")
        print("📱 访问地址: http://localhost:8001")
        print("🔑 默认账号: admin / admin")
        print("=" * 50)
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8001, 
            log_level="info",
            access_log=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
