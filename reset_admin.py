#!/usr/bin/env python3

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.security import get_password_hash
from app.db.database import SessionLocal
from app.models.user import User

def reset_admin_password():
    db = SessionLocal()
    try:
        # 查找admin用户
        admin_user = db.query(User).filter(User.username == 'admin').first()
        
        if admin_user:
            # 更新密码
            admin_user.hashed_password = get_password_hash('admin')
            db.commit()
            print("✅ Admin password updated successfully")
        else:
            # 创建新的admin用户
            new_admin = User(
                username='admin',
                email='<EMAIL>',
                hashed_password=get_password_hash('admin'),
                full_name='System Administrator',
                is_active=True,
                is_superuser=True
            )
            db.add(new_admin)
            db.commit()
            print("✅ New admin user created successfully")
            
        print("Username: admin")
        print("Password: admin")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    reset_admin_password()
