<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyCRM - 客户关系管理系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .dashboard {
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1890ff;
        }
        
        .stats-label {
            color: #666;
            margin-top: 5px;
        }
        
        .quick-action {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .hidden {
            display: none;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .alert-custom {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <h2 class="text-primary mb-2">📊 PyCRM</h2>
                <p class="text-muted">客户关系管理系统</p>
            </div>
            
            <div id="loginAlert" class="alert alert-danger alert-custom hidden" role="alert">
                登录失败，请检查用户名和密码
            </div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" value="admin" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" value="admin" required>
                </div>
                <button type="submit" class="btn btn-primary w-100" id="loginBtn">
                    <span id="loginSpinner" class="spinner-border spinner-border-sm hidden" role="status"></span>
                    登录
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">默认账号: admin / admin</small>
            </div>
        </div>
    </div>

    <!-- 仪表板页面 -->
    <div id="dashboardPage" class="hidden">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold text-primary" href="#">📊 PyCRM</a>
                <div class="d-flex">
                    <button class="btn btn-primary me-2">➕ 新建客户</button>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            👤 管理员
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">🚪 退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 仪表板内容 -->
        <div class="dashboard">
            <div class="container-fluid">
                <h2 class="mb-4">🎯 仪表板</h2>
                <p class="text-muted mb-4">欢迎使用 PyCRM 客户关系管理系统</p>
                
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="icon">👥</div>
                            <div class="stats-number" id="totalCustomers">0</div>
                            <div class="stats-label">总客户数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="icon">👤➕</div>
                            <div class="stats-number">5</div>
                            <div class="stats-label">本月新增</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="icon">💰</div>
                            <div class="stats-number">88,888</div>
                            <div class="stats-label">总收入</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="icon">🏆</div>
                            <div class="stats-number">15</div>
                            <div class="stats-label">活跃交易</div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="mb-3">快速操作</h4>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-action" onclick="alert('新建客户功能开发中...')">
                            <div class="icon">👤➕</div>
                            <h5>新建客户</h5>
                            <p class="text-muted">添加新的客户信息</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-action" onclick="alert('客户管理功能开发中...')">
                            <div class="icon">👥</div>
                            <h5>客户管理</h5>
                            <p class="text-muted">查看和管理所有客户</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="quick-action" onclick="alert('字段配置功能开发中...')">
                            <div class="icon">⚙️</div>
                            <h5>字段配置</h5>
                            <p class="text-muted">自定义客户字段</p>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="row">
                    <div class="col-12">
                        <div class="stats-card text-start">
                            <h5 class="mb-3">📋 系统信息</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>版本:</strong> 1.0.0</p>
                                    <p><strong>数据库:</strong> SQLite</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>后端:</strong> FastAPI + Python</p>
                                    <p><strong>前端:</strong> 纯HTML + JavaScript</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8001/api/v1';
        let authToken = localStorage.getItem('token');

        // 页面加载时检查登录状态
        window.onload = function() {
            if (authToken) {
                showDashboard();
                loadStats();
            } else {
                showLogin();
            }
        };

        // 显示登录页面
        function showLogin() {
            document.getElementById('loginPage').classList.remove('hidden');
            document.getElementById('dashboardPage').classList.add('hidden');
        }

        // 显示仪表板
        function showDashboard() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('dashboardPage').classList.remove('hidden');
        }

        // 登录处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginSpinner = document.getElementById('loginSpinner');
            const loginAlert = document.getElementById('loginAlert');
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginSpinner.classList.remove('hidden');
            loginAlert.classList.add('hidden');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    localStorage.setItem('token', authToken);
                    showDashboard();
                    loadStats();
                } else {
                    loginAlert.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Login error:', error);
                loginAlert.classList.remove('hidden');
            } finally {
                loginBtn.disabled = false;
                loginSpinner.classList.add('hidden');
            }
        });

        // 退出登录
        function logout() {
            authToken = null;
            localStorage.removeItem('token');
            showLogin();
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE_URL}/customers`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('totalCustomers').textContent = data.total || 0;
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }
    </script>
</body>
</html>
