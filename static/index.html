<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyCRM - 客户关系管理系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>"

    <!-- Ant Design CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@4.24.15/dist/antd.min.css">

    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>

    <!-- Ant Design -->
    <script src="https://unpkg.com/antd@4.24.15/dist/antd.min.js"></script>

    <!-- Ant Design Icons -->
    <script src="https://unpkg.com/@ant-design/icons@4.8.0/dist/index.umd.js"></script>

    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios@1.5.0/dist/axios.min.js"></script>

    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- React DnD for drag and drop -->
    <script src="https://unpkg.com/react-dnd@16.0.1/dist/umd/react-dnd.min.js"></script>
    <script src="https://unpkg.com/react-dnd-html5-backend@16.0.1/dist/umd/react-dnd-html5-backend.min.js"></script>

    <!-- Babel Standalone for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #root {
            height: 100vh;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }

        .login-card {
            width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .login-title {
            text-align: center;
            margin-bottom: 24px;
        }

        .login-title h2 {
            color: #1890ff;
            margin-bottom: 8px;
        }

        .dashboard-container {
            padding: 24px;
        }

        .stats-card {
            margin-bottom: 16px;
        }

        .quick-action-card {
            text-align: center;
            border-color: #1890ff;
            cursor: pointer;
            transition: all 0.3s;
        }

        .quick-action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        .sidebar {
            box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px;
            min-height: 600px;
        }

        .widget {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 16px;
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
        }

        .widget:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        .widget-title {
            font-weight: 600;
            color: #262626;
        }

        .widget-actions {
            display: flex;
            gap: 4px;
        }

        .widget-action {
            width: 20px;
            height: 20px;
            border: none;
            background: #f5f5f5;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .widget-action:hover {
            background: #e6f7ff;
            color: #1890ff;
        }

        .drop-zone {
            min-height: 200px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            transition: all 0.3s ease;
        }

        .drop-zone.drag-over {
            border-color: #1890ff;
            background-color: #f6ffed;
            color: #1890ff;
        }

        .widget-toolbar {
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }

        .widget-item {
            display: inline-block;
            padding: 8px 12px;
            margin: 4px;
            background: #f5f5f5;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.3s ease;
            border: 1px solid #d9d9d9;
        }

        .widget-item:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            transform: translateY(-1px);
        }

        .widget-item:active {
            cursor: grabbing;
        }

        .chart-container {
            width: 100%;
            height: 300px;
            position: relative;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, createContext, useContext } = React;
        const {
            Layout, Menu, Button, Card, Form, Input, Tabs, Typography, Space,
            Row, Col, Statistic, message, Table, Modal, Select, Descriptions,
            Avatar, Dropdown
        } = antd;

        // 使用Ant Design Icons
        const {
            UserOutlined, LockOutlined, MailOutlined, DashboardOutlined,
            TeamOutlined, SettingOutlined, LogoutOutlined, PlusOutlined,
            MenuFoldOutlined, MenuUnfoldOutlined, UserAddOutlined,
            DollarOutlined, TrophyOutlined, EditOutlined, DeleteOutlined,
            EyeOutlined, BarChartOutlined
        } = icons;

        const { Header, Sider, Content } = Layout;
        const { Title, Text, Paragraph } = Typography;
        const { TabPane } = Tabs;

        // API配置
        const API_BASE_URL = 'http://localhost:8001/api/v1';

        const api = axios.create({
            baseURL: API_BASE_URL,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
            },
        });

        // 请求拦截器
        api.interceptors.request.use(
            (config) => {
                const token = localStorage.getItem('token');
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        api.interceptors.response.use(
            (response) => {
                return response.data;
            },
            (error) => {
                if (error.response?.status === 401) {
                    localStorage.removeItem('token');
                    window.location.reload();
                }
                return Promise.reject(error);
            }
        );

        // Auth Context
        const AuthContext = createContext();

        const AuthProvider = ({ children }) => {
            const [user, setUser] = useState(null);
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                const token = localStorage.getItem('token');
                if (token) {
                    setUser({ token });
                }
            }, []);

            const login = async (credentials) => {
                try {
                    setLoading(true);
                    const response = await api.post('/auth/login', credentials);
                    const { access_token } = response;
                    localStorage.setItem('token', access_token);
                    setUser({ token: access_token });
                    message.success('登录成功');
                    return true;
                } catch (error) {
                    message.error('登录失败：' + (error.response?.data?.detail || '网络错误'));
                    return false;
                } finally {
                    setLoading(false);
                }
            };

            const logout = () => {
                localStorage.removeItem('token');
                setUser(null);
                message.success('已退出登录');
            };

            const register = async (userData) => {
                try {
                    setLoading(true);
                    await api.post('/auth/register', userData);
                    message.success('注册成功，请登录');
                    return true;
                } catch (error) {
                    message.error('注册失败：' + (error.response?.data?.detail || '网络错误'));
                    return false;
                } finally {
                    setLoading(false);
                }
            };

            return (
                <AuthContext.Provider value={{ user, login, logout, register, loading }}>
                    {children}
                </AuthContext.Provider>
            );
        };

        const useAuth = () => {
            const context = useContext(AuthContext);
            if (!context) {
                throw new Error('useAuth must be used within an AuthProvider');
            }
            return context;
        };

        // Login Component
        const Login = () => {
            const [activeTab, setActiveTab] = useState('login');
            const { login, register, loading } = useAuth();

            const onLogin = async (values) => {
                await login(values);
            };

            const onRegister = async (values) => {
                const success = await register(values);
                if (success) {
                    setActiveTab('login');
                }
            };

            return (
                <div className="login-container">
                    <Card className="login-card">
                        <div className="login-title">
                            <Title level={2}>PyCRM</Title>
                            <Text type="secondary">客户关系管理系统</Text>
                        </div>

                        <Tabs activeKey={activeTab} onChange={setActiveTab} centered>
                            <TabPane tab="登录" key="login">
                                <Form onFinish={onLogin} layout="vertical">
                                    <Form.Item
                                        name="username"
                                        rules={[{ required: true, message: '请输入用户名!' }]}
                                    >
                                        <Input
                                            prefix={<UserOutlined />}
                                            placeholder="用户名"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        name="password"
                                        rules={[{ required: true, message: '请输入密码!' }]}
                                    >
                                        <Input.Password
                                            prefix={<LockOutlined />}
                                            placeholder="密码"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            loading={loading}
                                            size="large"
                                            block
                                        >
                                            登录
                                        </Button>
                                    </Form.Item>
                                </Form>

                                <div style={{ textAlign: 'center', marginTop: 16 }}>
                                    <Text type="secondary">
                                        默认账号: admin / admin
                                    </Text>
                                </div>
                            </TabPane>

                            <TabPane tab="注册" key="register">
                                <Form onFinish={onRegister} layout="vertical">
                                    <Form.Item
                                        name="username"
                                        rules={[{ required: true, message: '请输入用户名!' }]}
                                    >
                                        <Input
                                            prefix={<UserOutlined />}
                                            placeholder="用户名"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        name="email"
                                        rules={[
                                            { required: true, message: '请输入邮箱!' },
                                            { type: 'email', message: '请输入有效的邮箱地址!' }
                                        ]}
                                    >
                                        <Input
                                            prefix={<MailOutlined />}
                                            placeholder="邮箱"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        name="full_name"
                                        rules={[{ required: true, message: '请输入姓名!' }]}
                                    >
                                        <Input
                                            prefix={<UserOutlined />}
                                            placeholder="姓名"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        name="password"
                                        rules={[
                                            { required: true, message: '请输入密码!' },
                                            { min: 6, message: '密码至少6位!' }
                                        ]}
                                    >
                                        <Input.Password
                                            prefix={<LockOutlined />}
                                            placeholder="密码"
                                            size="large"
                                        />
                                    </Form.Item>

                                    <Form.Item>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            loading={loading}
                                            size="large"
                                            block
                                        >
                                            注册
                                        </Button>
                                    </Form.Item>
                                </Form>
                            </TabPane>
                        </Tabs>
                    </Card>
                </div>
            );
        };

        // Chart Component
        const ChartWidget = ({ chartConfig, data }) => {
            const chartRef = React.useRef(null);
            const chartInstance = React.useRef(null);

            useEffect(() => {
                if (chartRef.current && data && data.length > 0) {
                    const ctx = chartRef.current.getContext('2d');

                    // 销毁之前的图表实例
                    if (chartInstance.current) {
                        chartInstance.current.destroy();
                    }

                    const config = {
                        type: chartConfig.type || 'bar',
                        data: {
                            labels: data.map(item => item[chartConfig.xField] || item.label),
                            datasets: [{
                                label: chartConfig.title || '数据',
                                data: data.map(item => item[chartConfig.yField] || item.value),
                                backgroundColor: [
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(255, 205, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)',
                                    'rgba(153, 102, 255, 0.8)',
                                    'rgba(255, 159, 64, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(255, 205, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)',
                                    'rgba(255, 159, 64, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: chartConfig.type === 'pie'
                                }
                            },
                            scales: chartConfig.type !== 'pie' ? {
                                y: {
                                    beginAtZero: true
                                }
                            } : {}
                        }
                    };

                    chartInstance.current = new Chart(ctx, config);
                }

                return () => {
                    if (chartInstance.current) {
                        chartInstance.current.destroy();
                    }
                };
            }, [chartConfig, data]);

            return (
                <div className="chart-container">
                    <canvas ref={chartRef}></canvas>
                </div>
            );
        };

        // Widget Component
        const Widget = ({ widget, onRemove, onEdit }) => {
            const renderContent = () => {
                switch (widget.type) {
                    case 'chart':
                        return <ChartWidget chartConfig={widget.config} data={widget.data} />;
                    case 'stats':
                        return (
                            <div style={{ textAlign: 'center' }}>
                                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1890ff' }}>
                                    {widget.value}
                                </div>
                                <div style={{ color: '#666', marginTop: '8px' }}>
                                    {widget.label}
                                </div>
                            </div>
                        );
                    case 'text':
                        return (
                            <div>
                                <p>{widget.content}</p>
                            </div>
                        );
                    case 'list':
                        return (
                            <div>
                                {widget.items && widget.items.map((item, index) => (
                                    <div key={index} style={{ padding: '4px 0', borderBottom: '1px solid #f0f0f0' }}>
                                        {item}
                                    </div>
                                ))}
                            </div>
                        );
                    default:
                        return <div>未知组件类型</div>;
                }
            };

            return (
                <div
                    className="widget"
                    style={{
                        gridColumn: `span ${widget.width || 4}`,
                        gridRow: `span ${widget.height || 2}`
                    }}
                >
                    <div className="widget-header">
                        <div className="widget-title">{widget.title}</div>
                        <div className="widget-actions">
                            <button className="widget-action" onClick={() => onEdit && onEdit(widget)}>
                                ✏️
                            </button>
                            <button className="widget-action" onClick={() => onRemove && onRemove(widget.id)}>
                                ❌
                            </button>
                        </div>
                    </div>
                    {renderContent()}
                </div>
            );
        };

        // Dashboard Component
        const Dashboard = ({ onNavigate }) => {
            const [stats, setStats] = useState({
                totalCustomers: 0,
                newCustomers: 5,
                totalRevenue: 88888,
                activeDeals: 15,
            });
            const [loading, setLoading] = useState(false);
            const [widgets, setWidgets] = useState([]);
            const [editMode, setEditMode] = useState(false);
            const [availableCharts, setAvailableCharts] = useState([]);
            const [editingWidget, setEditingWidget] = useState(null);
            const [widgetModalVisible, setWidgetModalVisible] = useState(false);
            const [widgetForm] = Form.useForm();

            useEffect(() => {
                loadStats();
                loadDefaultWidgets();
                loadAvailableCharts();
            }, []);

            const loadStats = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/customers');
                    setStats(prev => ({
                        ...prev,
                        totalCustomers: response.total || 0,
                    }));
                } catch (error) {
                    console.error('Failed to load stats:', error);
                } finally {
                    setLoading(false);
                }
            };

            const loadAvailableCharts = async () => {
                try {
                    const response = await api.get('/charts');
                    setAvailableCharts(response.charts || []);
                } catch (error) {
                    console.error('Failed to load charts:', error);
                }
            };

            const loadDefaultWidgets = async () => {
                try {
                    // 加载默认图表数据
                    const chartsResponse = await api.get('/charts');
                    const charts = chartsResponse.charts || [];

                    const defaultWidgets = [
                        {
                            id: 1,
                            type: 'stats',
                            title: '总客户数',
                            value: stats.totalCustomers,
                            label: '客户',
                            width: 3,
                            height: 1
                        },
                        {
                            id: 2,
                            type: 'stats',
                            title: '本月新增',
                            value: stats.newCustomers,
                            label: '新客户',
                            width: 3,
                            height: 1
                        },
                        {
                            id: 3,
                            type: 'stats',
                            title: '总收入',
                            value: '¥' + stats.totalRevenue.toLocaleString(),
                            label: '收入',
                            width: 3,
                            height: 1
                        },
                        {
                            id: 4,
                            type: 'stats',
                            title: '活跃交易',
                            value: stats.activeDeals,
                            label: '交易',
                            width: 3,
                            height: 1
                        }
                    ];

                    // 添加数据库中的图表
                    for (let i = 0; i < Math.min(charts.length, 4); i++) {
                        const chart = charts[i];
                        try {
                            const dataResponse = await api.get(`/charts/${chart.id}/data`);
                            defaultWidgets.push({
                                id: 100 + chart.id,
                                type: 'chart',
                                title: chart.title,
                                config: JSON.parse(chart.chart_config || '{}'),
                                data: dataResponse.data || [],
                                width: 6,
                                height: 3
                            });
                        } catch (error) {
                            console.error(`Failed to load chart ${chart.id}:`, error);
                        }
                    }

                    // 如果没有图表，添加默认图表
                    if (charts.length === 0) {
                        defaultWidgets.push(
                            {
                                id: 5,
                                type: 'chart',
                                title: '客户增长趋势',
                                config: {
                                    type: 'line',
                                    xField: 'month',
                                    yField: 'count'
                                },
                                data: [
                                    { month: '1月', count: 12 },
                                    { month: '2月', count: 18 },
                                    { month: '3月', count: 25 },
                                    { month: '4月', count: 20 },
                                    { month: '5月', count: 30 },
                                    { month: '6月', count: 28 }
                                ],
                                width: 6,
                                height: 3
                            },
                            {
                                id: 6,
                                type: 'chart',
                                title: '收入分析',
                                config: {
                                    type: 'bar',
                                    xField: 'month',
                                    yField: 'revenue'
                                },
                                data: [
                                    { month: '1月', revenue: 15000 },
                                    { month: '2月', revenue: 22000 },
                                    { month: '3月', revenue: 31000 },
                                    { month: '4月', revenue: 28000 },
                                    { month: '5月', revenue: 35000 },
                                    { month: '6月', revenue: 33000 }
                                ],
                                width: 6,
                                height: 3
                            }
                        );
                    }

                    setWidgets(defaultWidgets);
                } catch (error) {
                    console.error('Failed to load default widgets:', error);
                    // 如果加载失败，使用基本的统计组件
                    const basicWidgets = [
                        {
                            id: 1,
                            type: 'stats',
                            title: '总客户数',
                            value: stats.totalCustomers,
                            label: '客户',
                            width: 3,
                            height: 1
                        },
                        {
                            id: 2,
                            type: 'stats',
                            title: '本月新增',
                            value: stats.newCustomers,
                            label: '新客户',
                            width: 3,
                            height: 1
                        }
                    ];
                    setWidgets(basicWidgets);
                }
            };

            const addWidget = (widgetType) => {
                const newWidget = {
                    id: Date.now(),
                    type: widgetType,
                    title: `新${widgetType === 'chart' ? '图表' : widgetType === 'stats' ? '统计' : '组件'}`,
                    width: 4,
                    height: 2
                };

                if (widgetType === 'stats') {
                    newWidget.value = 0;
                    newWidget.label = '数据';
                } else if (widgetType === 'chart') {
                    newWidget.config = { type: 'bar', xField: 'x', yField: 'y' };
                    newWidget.data = [];
                } else if (widgetType === 'text') {
                    newWidget.content = '这是一个文本组件';
                } else if (widgetType === 'list') {
                    newWidget.items = ['项目1', '项目2', '项目3'];
                }

                setWidgets([...widgets, newWidget]);
            };

            const removeWidget = (widgetId) => {
                setWidgets(widgets.filter(w => w.id !== widgetId));
            };

            const editWidget = (widget) => {
                setEditingWidget(widget);
                setWidgetModalVisible(true);

                // 根据组件类型设置表单值
                if (widget.type === 'stats') {
                    widgetForm.setFieldsValue({
                        title: widget.title,
                        value: widget.value,
                        label: widget.label,
                        width: widget.width,
                        height: widget.height
                    });
                } else if (widget.type === 'chart') {
                    widgetForm.setFieldsValue({
                        title: widget.title,
                        chartType: widget.config?.type || 'bar',
                        xField: widget.config?.xField || '',
                        yField: widget.config?.yField || '',
                        width: widget.width,
                        height: widget.height
                    });
                } else if (widget.type === 'text') {
                    widgetForm.setFieldsValue({
                        title: widget.title,
                        content: widget.content,
                        width: widget.width,
                        height: widget.height
                    });
                } else if (widget.type === 'list') {
                    widgetForm.setFieldsValue({
                        title: widget.title,
                        items: widget.items?.join('\n') || '',
                        width: widget.width,
                        height: widget.height
                    });
                }
            };

            const handleWidgetSubmit = (values) => {
                const updatedWidget = { ...editingWidget };

                // 更新基本属性
                updatedWidget.title = values.title;
                updatedWidget.width = values.width || 4;
                updatedWidget.height = values.height || 2;

                // 根据组件类型更新特定属性
                if (editingWidget.type === 'stats') {
                    updatedWidget.value = values.value;
                    updatedWidget.label = values.label;
                } else if (editingWidget.type === 'chart') {
                    updatedWidget.config = {
                        ...updatedWidget.config,
                        type: values.chartType,
                        xField: values.xField,
                        yField: values.yField
                    };
                    // 如果是自定义图表，生成新的模拟数据
                    if (!updatedWidget.data || updatedWidget.data.length === 0) {
                        updatedWidget.data = generateMockChartData(values.chartType, values.xField, values.yField);
                    }
                } else if (editingWidget.type === 'text') {
                    updatedWidget.content = values.content;
                } else if (editingWidget.type === 'list') {
                    updatedWidget.items = values.items ? values.items.split('\n').filter(item => item.trim()) : [];
                }

                // 更新组件列表
                const newWidgets = widgets.map(w => w.id === editingWidget.id ? updatedWidget : w);
                setWidgets(newWidgets);

                setWidgetModalVisible(false);
                setEditingWidget(null);
                widgetForm.resetFields();
                message.success('组件更新成功');
            };

            const generateMockChartData = (chartType, xField, yField) => {
                const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
                return months.map(month => ({
                    [xField]: month,
                    [yField]: Math.floor(Math.random() * 50) + 10,
                    month: month,
                    count: Math.floor(Math.random() * 50) + 10,
                    revenue: Math.floor(Math.random() * 30000) + 10000,
                    value: Math.floor(Math.random() * 100) + 20
                }));
            };

            const addChartWidget = async (chartId) => {
                try {
                    const chartResponse = await api.get(`/charts/${chartId}`);
                    const dataResponse = await api.get(`/charts/${chartId}/data`);

                    const newWidget = {
                        id: Date.now(),
                        type: 'chart',
                        title: chartResponse.title,
                        config: JSON.parse(chartResponse.chart_config),
                        data: dataResponse.data,
                        width: 6,
                        height: 3
                    };

                    setWidgets([...widgets, newWidget]);
                    message.success('图表添加成功');
                } catch (error) {
                    message.error('添加图表失败');
                }
            };

            return (
                <div>
                    <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>🎯 仪表板</Title>
                            <Paragraph type="secondary">
                                欢迎使用 PyCRM 客户关系管理系统 - 可拖拽自定义仪表板
                            </Paragraph>
                        </div>
                        <Space>
                            <Button
                                type={editMode ? 'primary' : 'default'}
                                onClick={() => setEditMode(!editMode)}
                            >
                                {editMode ? '完成编辑' : '编辑模式'}
                            </Button>
                            <Button onClick={() => onNavigate && onNavigate('chart-builder')}>
                                图表管理
                            </Button>
                        </Space>
                    </div>

                    {editMode && (
                        <div className="widget-toolbar">
                            <Title level={4} style={{ marginBottom: 16 }}>添加组件</Title>
                            <Space wrap>
                                <div className="widget-item" onClick={() => addWidget('stats')}>
                                    📊 统计卡片
                                </div>
                                <div className="widget-item" onClick={() => addWidget('chart')}>
                                    📈 图表组件
                                </div>
                                <div className="widget-item" onClick={() => addWidget('text')}>
                                    📝 文本组件
                                </div>
                                <div className="widget-item" onClick={() => addWidget('list')}>
                                    📋 列表组件
                                </div>
                            </Space>

                            {availableCharts.length > 0 && (
                                <div style={{ marginTop: 16 }}>
                                    <Title level={5}>可用图表</Title>
                                    <Space wrap>
                                        {availableCharts.map(chart => (
                                            <div
                                                key={chart.id}
                                                className="widget-item"
                                                onClick={() => addChartWidget(chart.id)}
                                            >
                                                📊 {chart.title}
                                            </div>
                                        ))}
                                    </Space>
                                </div>
                            )}
                        </div>
                    )}

                    <div className="dashboard-grid">
                        {widgets.map(widget => (
                            <Widget
                                key={widget.id}
                                widget={widget}
                                onRemove={editMode ? removeWidget : null}
                                onEdit={editMode ? editWidget : null}
                            />
                        ))}

                        {editMode && widgets.length === 0 && (
                            <div className="drop-zone" style={{ gridColumn: 'span 12' }}>
                                <div>
                                    <p>拖拽上方组件到这里开始构建您的仪表板</p>
                                    <p style={{ fontSize: '12px', color: '#999' }}>或点击上方组件直接添加</p>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* 组件编辑模态框 */}
                    <Modal
                        title={`编辑${editingWidget?.type === 'chart' ? '图表' : editingWidget?.type === 'stats' ? '统计' : editingWidget?.type === 'text' ? '文本' : '列表'}组件`}
                        visible={widgetModalVisible}
                        onCancel={() => {
                            setWidgetModalVisible(false);
                            setEditingWidget(null);
                            widgetForm.resetFields();
                        }}
                        onOk={() => widgetForm.submit()}
                        width={600}
                    >
                        <Form
                            form={widgetForm}
                            layout="vertical"
                            onFinish={handleWidgetSubmit}
                        >
                            <Form.Item
                                name="title"
                                label="组件标题"
                                rules={[{ required: true, message: '请输入组件标题' }]}
                            >
                                <Input placeholder="请输入组件标题" />
                            </Form.Item>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="width"
                                        label="宽度 (1-12)"
                                        rules={[{ required: true, message: '请输入宽度' }]}
                                    >
                                        <Input type="number" min={1} max={12} placeholder="4" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="height"
                                        label="高度 (1-6)"
                                        rules={[{ required: true, message: '请输入高度' }]}
                                    >
                                        <Input type="number" min={1} max={6} placeholder="2" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            {/* 统计组件特有字段 */}
                            {editingWidget?.type === 'stats' && (
                                <>
                                    <Form.Item
                                        name="value"
                                        label="数值"
                                        rules={[{ required: true, message: '请输入数值' }]}
                                    >
                                        <Input placeholder="请输入数值" />
                                    </Form.Item>
                                    <Form.Item
                                        name="label"
                                        label="标签"
                                        rules={[{ required: true, message: '请输入标签' }]}
                                    >
                                        <Input placeholder="请输入标签" />
                                    </Form.Item>
                                </>
                            )}

                            {/* 图表组件特有字段 */}
                            {editingWidget?.type === 'chart' && (
                                <>
                                    <Form.Item
                                        name="chartType"
                                        label="图表类型"
                                        rules={[{ required: true, message: '请选择图表类型' }]}
                                    >
                                        <Select placeholder="选择图表类型">
                                            <Select.Option value="bar">柱状图</Select.Option>
                                            <Select.Option value="line">折线图</Select.Option>
                                            <Select.Option value="pie">饼图</Select.Option>
                                            <Select.Option value="area">面积图</Select.Option>
                                        </Select>
                                    </Form.Item>
                                    <Row gutter={16}>
                                        <Col span={12}>
                                            <Form.Item
                                                name="xField"
                                                label="X轴字段"
                                                rules={[{ required: true, message: '请输入X轴字段' }]}
                                            >
                                                <Input placeholder="例如: month" />
                                            </Form.Item>
                                        </Col>
                                        <Col span={12}>
                                            <Form.Item
                                                name="yField"
                                                label="Y轴字段"
                                                rules={[{ required: true, message: '请输入Y轴字段' }]}
                                            >
                                                <Input placeholder="例如: count" />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                </>
                            )}

                            {/* 文本组件特有字段 */}
                            {editingWidget?.type === 'text' && (
                                <Form.Item
                                    name="content"
                                    label="文本内容"
                                    rules={[{ required: true, message: '请输入文本内容' }]}
                                >
                                    <Input.TextArea rows={4} placeholder="请输入文本内容" />
                                </Form.Item>
                            )}

                            {/* 列表组件特有字段 */}
                            {editingWidget?.type === 'list' && (
                                <Form.Item
                                    name="items"
                                    label="列表项 (每行一项)"
                                    rules={[{ required: true, message: '请输入列表项' }]}
                                >
                                    <Input.TextArea rows={4} placeholder="项目1&#10;项目2&#10;项目3" />
                                </Form.Item>
                            )}
                        </Form>
                    </Modal>
                </div>
            );
        };

        // Customers Component
        const Customers = () => {
            const [customers, setCustomers] = useState([]);
            const [loading, setLoading] = useState(false);
            const [modalVisible, setModalVisible] = useState(false);
            const [editingCustomer, setEditingCustomer] = useState(null);
            const [customFields, setCustomFields] = useState([]);
            const [form] = Form.useForm();

            useEffect(() => {
                loadCustomers();
                loadCustomFields();
            }, []);

            const loadCustomers = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/customers');
                    setCustomers(response.customers || []);
                } catch (error) {
                    message.error('加载客户列表失败');
                } finally {
                    setLoading(false);
                }
            };

            const loadCustomFields = async () => {
                try {
                    const response = await api.get('/custom-fields');
                    setCustomFields(response.fields || []);
                } catch (error) {
                    console.error('Failed to load custom fields:', error);
                }
            };

            const handleAdd = () => {
                setEditingCustomer(null);
                form.resetFields();
                setModalVisible(true);
            };

            const handleEdit = (customer) => {
                setEditingCustomer(customer);
                form.setFieldsValue(customer);
                setModalVisible(true);
            };

            const handleDelete = async (id) => {
                try {
                    await api.delete(`/customers/${id}`);
                    message.success('删除成功');
                    loadCustomers();
                } catch (error) {
                    message.error('删除失败');
                }
            };

            const handleSubmit = async (values) => {
                try {
                    if (editingCustomer) {
                        await api.put(`/customers/${editingCustomer.id}`, values);
                        message.success('更新成功');
                    } else {
                        await api.post('/customers', values);
                        message.success('创建成功');
                    }
                    setModalVisible(false);
                    loadCustomers();
                } catch (error) {
                    message.error(editingCustomer ? '更新失败' : '创建失败');
                }
            };

            const columns = [
                {
                    title: '姓名',
                    dataIndex: 'name',
                    key: 'name',
                    render: (text) => <Text strong>{text}</Text>,
                },
                {
                    title: '邮箱',
                    dataIndex: 'email',
                    key: 'email',
                },
                {
                    title: '电话',
                    dataIndex: 'phone',
                    key: 'phone',
                },
                {
                    title: '公司',
                    dataIndex: 'company',
                    key: 'company',
                },
                {
                    title: '创建时间',
                    dataIndex: 'created_at',
                    key: 'created_at',
                    render: (text) => text ? new Date(text).toLocaleDateString() : '-',
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                        <Space size="middle">
                            <Button
                                type="link"
                                icon={<EyeOutlined />}
                                onClick={() => message.info('查看详情功能开发中')}
                            >
                                查看
                            </Button>
                            <Button
                                type="link"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            >
                                编辑
                            </Button>
                            <Button
                                type="link"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认删除',
                                        content: `确定要删除客户 "${record.name}" 吗？`,
                                        onOk: () => handleDelete(record.id),
                                    });
                                }}
                            >
                                删除
                            </Button>
                        </Space>
                    ),
                },
            ];

            return (
                <div>
                    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>👥 客户管理</Title>
                            <Paragraph type="secondary">
                                管理您的客户信息和联系方式
                            </Paragraph>
                        </div>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size="large"
                        >
                            新建客户
                        </Button>
                    </div>

                    <Card>
                        <Table
                            columns={columns}
                            dataSource={customers}
                            loading={loading}
                            rowKey="id"
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条记录`,
                            }}
                        />
                    </Card>

                    <Modal
                        title={editingCustomer ? '编辑客户' : '新建客户'}
                        visible={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        onOk={() => form.submit()}
                        width={600}
                    >
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleSubmit}
                        >
                            <Form.Item
                                name="name"
                                label="姓名"
                                rules={[{ required: true, message: '请输入姓名' }]}
                            >
                                <Input placeholder="请输入姓名" />
                            </Form.Item>

                            <Form.Item
                                name="email"
                                label="邮箱"
                                rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
                            >
                                <Input placeholder="请输入邮箱" />
                            </Form.Item>

                            <Form.Item name="phone" label="电话">
                                <Input placeholder="请输入电话" />
                            </Form.Item>

                            <Form.Item name="company" label="公司">
                                <Input placeholder="请输入公司名称" />
                            </Form.Item>

                            <Form.Item name="address" label="地址">
                                <Input.TextArea placeholder="请输入地址" rows={2} />
                            </Form.Item>

                            {/* 动态渲染自定义字段 */}
                            {customFields.map(field => (
                                <Form.Item
                                    key={field.name}
                                    name={field.name}
                                    label={field.label}
                                    rules={field.is_required === 'true' ? [{ required: true, message: `请输入${field.label}` }] : []}
                                >
                                    {field.field_type === 'textarea' ? (
                                        <Input.TextArea placeholder={`请输入${field.label}`} rows={2} />
                                    ) : field.field_type === 'select' && field.options ? (
                                        <Select placeholder={`请选择${field.label}`}>
                                            {JSON.parse(field.options).map(option => (
                                                <Select.Option key={option} value={option}>{option}</Select.Option>
                                            ))}
                                        </Select>
                                    ) : (
                                        <Input
                                            type={field.field_type === 'number' ? 'number' : 'text'}
                                            placeholder={`请输入${field.label}`}
                                        />
                                    )}
                                </Form.Item>
                            ))}

                            <Form.Item name="notes" label="备注">
                                <Input.TextArea placeholder="请输入备注" rows={3} />
                            </Form.Item>
                        </Form>
                    </Modal>
                </div>
            );
        };

        // Custom Fields Component
        const CustomFields = () => {
            const [fields, setFields] = useState([]);
            const [loading, setLoading] = useState(false);
            const [modalVisible, setModalVisible] = useState(false);
            const [editingField, setEditingField] = useState(null);
            const [form] = Form.useForm();

            useEffect(() => {
                loadFields();
            }, []);

            const loadFields = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/custom-fields');
                    setFields(response.fields || []);
                } catch (error) {
                    message.error('加载字段列表失败');
                } finally {
                    setLoading(false);
                }
            };

            const handleAdd = () => {
                setEditingField(null);
                form.resetFields();
                setModalVisible(true);
            };

            const handleEdit = (field) => {
                setEditingField(field);
                const fieldData = { ...field };
                if (field.options) {
                    fieldData.options = JSON.parse(field.options);
                }
                form.setFieldsValue(fieldData);
                setModalVisible(true);
            };

            const handleDelete = async (id) => {
                try {
                    await api.delete(`/custom-fields/${id}`);
                    message.success('删除成功');
                    loadFields();
                } catch (error) {
                    message.error('删除失败');
                }
            };

            const handleSubmit = async (values) => {
                try {
                    const submitData = { ...values };
                    if (values.options && Array.isArray(values.options)) {
                        submitData.options = JSON.stringify(values.options);
                    }

                    if (editingField) {
                        await api.put(`/custom-fields/${editingField.id}`, submitData);
                        message.success('更新成功');
                    } else {
                        await api.post('/custom-fields', submitData);
                        message.success('创建成功');
                    }
                    setModalVisible(false);
                    loadFields();
                } catch (error) {
                    message.error(editingField ? '更新失败' : '创建失败');
                }
            };

            const columns = [
                {
                    title: '字段名称',
                    dataIndex: 'name',
                    key: 'name',
                    render: (text) => <Text code>{text}</Text>,
                },
                {
                    title: '显示标签',
                    dataIndex: 'label',
                    key: 'label',
                    render: (text) => <Text strong>{text}</Text>,
                },
                {
                    title: '字段类型',
                    dataIndex: 'field_type',
                    key: 'field_type',
                    render: (type) => {
                        const typeMap = {
                            'text': '文本',
                            'number': '数字',
                            'email': '邮箱',
                            'textarea': '多行文本',
                            'select': '下拉选择',
                            'date': '日期'
                        };
                        return <Text>{typeMap[type] || type}</Text>;
                    },
                },
                {
                    title: '是否必填',
                    dataIndex: 'is_required',
                    key: 'is_required',
                    render: (required) => (
                        <Text type={required === 'true' ? 'danger' : 'secondary'}>
                            {required === 'true' ? '必填' : '可选'}
                        </Text>
                    ),
                },
                {
                    title: '创建时间',
                    dataIndex: 'created_at',
                    key: 'created_at',
                    render: (text) => text ? new Date(text).toLocaleDateString() : '-',
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                        <Space size="middle">
                            <Button
                                type="link"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            >
                                编辑
                            </Button>
                            <Button
                                type="link"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认删除',
                                        content: `确定要删除字段 "${record.label}" 吗？`,
                                        onOk: () => handleDelete(record.id),
                                    });
                                }}
                            >
                                删除
                            </Button>
                        </Space>
                    ),
                },
            ];

            return (
                <div>
                    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>⚙️ 字段配置</Title>
                            <Paragraph type="secondary">
                                自定义客户信息字段，满足不同业务需求
                            </Paragraph>
                        </div>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size="large"
                        >
                            新建字段
                        </Button>
                    </div>

                    <Card>
                        <Table
                            columns={columns}
                            dataSource={fields}
                            loading={loading}
                            rowKey="id"
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条记录`,
                            }}
                        />
                    </Card>

                    <Modal
                        title={editingField ? '编辑字段' : '新建字段'}
                        visible={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        onOk={() => form.submit()}
                        width={600}
                    >
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleSubmit}
                        >
                            <Form.Item
                                name="name"
                                label="字段名称"
                                rules={[
                                    { required: true, message: '请输入字段名称' },
                                    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名称只能包含字母、数字和下划线，且以字母或下划线开头' }
                                ]}
                            >
                                <Input placeholder="例如: customer_level" />
                            </Form.Item>

                            <Form.Item
                                name="label"
                                label="显示标签"
                                rules={[{ required: true, message: '请输入显示标签' }]}
                            >
                                <Input placeholder="例如: 客户等级" />
                            </Form.Item>

                            <Form.Item
                                name="field_type"
                                label="字段类型"
                                rules={[{ required: true, message: '请选择字段类型' }]}
                            >
                                <Select placeholder="请选择字段类型">
                                    <Select.Option value="text">文本</Select.Option>
                                    <Select.Option value="number">数字</Select.Option>
                                    <Select.Option value="email">邮箱</Select.Option>
                                    <Select.Option value="textarea">多行文本</Select.Option>
                                    <Select.Option value="select">下拉选择</Select.Option>
                                    <Select.Option value="date">日期</Select.Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="is_required"
                                label="是否必填"
                                rules={[{ required: true, message: '请选择是否必填' }]}
                            >
                                <Select placeholder="请选择是否必填">
                                    <Select.Option value="true">必填</Select.Option>
                                    <Select.Option value="false">可选</Select.Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) =>
                                    prevValues.field_type !== currentValues.field_type
                                }
                            >
                                {({ getFieldValue }) => {
                                    const fieldType = getFieldValue('field_type');
                                    return fieldType === 'select' ? (
                                        <Form.Item
                                            name="options"
                                            label="选项列表"
                                            rules={[{ required: true, message: '请输入选项列表' }]}
                                        >
                                            <Select
                                                mode="tags"
                                                placeholder="输入选项后按回车添加"
                                                style={{ width: '100%' }}
                                            />
                                        </Form.Item>
                                    ) : null;
                                }}
                            </Form.Item>

                            <Form.Item name="description" label="字段描述">
                                <Input.TextArea placeholder="请输入字段描述（可选）" rows={2} />
                            </Form.Item>
                        </Form>
                    </Modal>
                </div>
            );
        };

        // Settings Component
        const Settings = () => {
            const [activeTab, setActiveTab] = useState('profile');
            const [userProfile, setUserProfile] = useState({});
            const [systemInfo, setSystemInfo] = useState({});
            const [loading, setLoading] = useState(false);
            const [profileForm] = Form.useForm();
            const [passwordForm] = Form.useForm();

            useEffect(() => {
                loadUserProfile();
                loadSystemInfo();
            }, []);

            const loadUserProfile = async () => {
                try {
                    const response = await api.get('/settings/profile');
                    setUserProfile(response);
                    profileForm.setFieldsValue(response);
                } catch (error) {
                    message.error('加载用户资料失败');
                }
            };

            const loadSystemInfo = async () => {
                try {
                    const response = await api.get('/settings/system');
                    setSystemInfo(response);
                } catch (error) {
                    console.error('Failed to load system info:', error);
                }
            };

            const handleProfileUpdate = async (values) => {
                try {
                    setLoading(true);
                    await api.put('/settings/profile', values);
                    message.success('资料更新成功');
                    loadUserProfile();
                } catch (error) {
                    message.error('更新失败：' + (error.response?.data?.detail || '网络错误'));
                } finally {
                    setLoading(false);
                }
            };

            const handlePasswordChange = async (values) => {
                try {
                    setLoading(true);
                    await api.post('/settings/change-password', {
                        current_password: values.current_password,
                        new_password: values.new_password
                    });
                    message.success('密码修改成功');
                    passwordForm.resetFields();
                } catch (error) {
                    message.error('修改失败：' + (error.response?.data?.detail || '网络错误'));
                } finally {
                    setLoading(false);
                }
            };

            const handleBackup = async () => {
                try {
                    setLoading(true);
                    const response = await api.post('/settings/system/backup');
                    message.success('备份创建成功：' + response.backup_file);
                } catch (error) {
                    message.error('备份失败：' + (error.response?.data?.detail || '网络错误'));
                } finally {
                    setLoading(false);
                }
            };

            const testDatabaseConnection = async () => {
                try {
                    setLoading(true);
                    const response = await api.post('/settings/database/test-connection', {
                        db_type: 'sqlite'
                    });
                    if (response.status === 'success') {
                        message.success(response.message);
                    } else {
                        message.error(response.message);
                    }
                } catch (error) {
                    message.error('连接测试失败');
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div>
                    <div style={{ marginBottom: 16 }}>
                        <Title level={2}>⚙️ 系统设置</Title>
                        <Paragraph type="secondary">
                            管理系统配置、用户资料和数据库设置
                        </Paragraph>
                    </div>

                    <Card>
                        <Tabs activeKey={activeTab} onChange={setActiveTab}>
                            <TabPane tab="个人资料" key="profile">
                                <Row gutter={24}>
                                    <Col span={12}>
                                        <Card title="基本信息" size="small">
                                            <Form
                                                form={profileForm}
                                                layout="vertical"
                                                onFinish={handleProfileUpdate}
                                            >
                                                <Form.Item
                                                    name="username"
                                                    label="用户名"
                                                    rules={[{ required: true, message: '请输入用户名' }]}
                                                >
                                                    <Input placeholder="请输入用户名" />
                                                </Form.Item>

                                                <Form.Item
                                                    name="email"
                                                    label="邮箱"
                                                    rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
                                                >
                                                    <Input placeholder="请输入邮箱" />
                                                </Form.Item>

                                                <Form.Item
                                                    name="full_name"
                                                    label="姓名"
                                                >
                                                    <Input placeholder="请输入姓名" />
                                                </Form.Item>

                                                <Form.Item>
                                                    <Button type="primary" htmlType="submit" loading={loading}>
                                                        更新资料
                                                    </Button>
                                                </Form.Item>
                                            </Form>
                                        </Card>
                                    </Col>
                                    <Col span={12}>
                                        <Card title="修改密码" size="small">
                                            <Form
                                                form={passwordForm}
                                                layout="vertical"
                                                onFinish={handlePasswordChange}
                                            >
                                                <Form.Item
                                                    name="current_password"
                                                    label="当前密码"
                                                    rules={[{ required: true, message: '请输入当前密码' }]}
                                                >
                                                    <Input.Password placeholder="请输入当前密码" />
                                                </Form.Item>

                                                <Form.Item
                                                    name="new_password"
                                                    label="新密码"
                                                    rules={[
                                                        { required: true, message: '请输入新密码' },
                                                        { min: 6, message: '密码至少6位' }
                                                    ]}
                                                >
                                                    <Input.Password placeholder="请输入新密码" />
                                                </Form.Item>

                                                <Form.Item
                                                    name="confirm_password"
                                                    label="确认密码"
                                                    dependencies={['new_password']}
                                                    rules={[
                                                        { required: true, message: '请确认新密码' },
                                                        ({ getFieldValue }) => ({
                                                            validator(_, value) {
                                                                if (!value || getFieldValue('new_password') === value) {
                                                                    return Promise.resolve();
                                                                }
                                                                return Promise.reject(new Error('两次输入的密码不一致'));
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <Input.Password placeholder="请确认新密码" />
                                                </Form.Item>

                                                <Form.Item>
                                                    <Button type="primary" htmlType="submit" loading={loading}>
                                                        修改密码
                                                    </Button>
                                                </Form.Item>
                                            </Form>
                                        </Card>
                                    </Col>
                                </Row>
                            </TabPane>

                            <TabPane tab="数据库设置" key="database">
                                <Row gutter={24}>
                                    <Col span={12}>
                                        <Card title="当前数据库" size="small">
                                            <Descriptions column={1}>
                                                <Descriptions.Item label="数据库类型">SQLite</Descriptions.Item>
                                                <Descriptions.Item label="数据库文件">crm.db</Descriptions.Item>
                                                <Descriptions.Item label="状态">
                                                    <Text type="success">已连接</Text>
                                                </Descriptions.Item>
                                            </Descriptions>
                                            <div style={{ marginTop: 16 }}>
                                                <Space>
                                                    <Button onClick={testDatabaseConnection} loading={loading}>
                                                        测试连接
                                                    </Button>
                                                    <Button type="primary" onClick={handleBackup} loading={loading}>
                                                        创建备份
                                                    </Button>
                                                </Space>
                                            </div>
                                        </Card>
                                    </Col>
                                    <Col span={12}>
                                        <Card title="MySQL配置" size="small">
                                            <Paragraph type="secondary">
                                                配置MySQL数据库连接（功能开发中）
                                            </Paragraph>
                                            <Form layout="vertical">
                                                <Form.Item label="主机地址">
                                                    <Input placeholder="localhost" disabled />
                                                </Form.Item>
                                                <Form.Item label="端口">
                                                    <Input placeholder="3306" disabled />
                                                </Form.Item>
                                                <Form.Item label="数据库名">
                                                    <Input placeholder="pycrm" disabled />
                                                </Form.Item>
                                                <Form.Item label="用户名">
                                                    <Input placeholder="root" disabled />
                                                </Form.Item>
                                                <Form.Item label="密码">
                                                    <Input.Password placeholder="password" disabled />
                                                </Form.Item>
                                                <Form.Item>
                                                    <Button disabled>测试连接</Button>
                                                </Form.Item>
                                            </Form>
                                        </Card>
                                    </Col>
                                </Row>
                            </TabPane>

                            <TabPane tab="系统信息" key="system">
                                <Row gutter={24}>
                                    <Col span={12}>
                                        <Card title="系统状态" size="small">
                                            <Descriptions column={1}>
                                                <Descriptions.Item label="系统名称">{systemInfo.system_name}</Descriptions.Item>
                                                <Descriptions.Item label="版本">{systemInfo.system_version}</Descriptions.Item>
                                                <Descriptions.Item label="数据库类型">{systemInfo.database_type}</Descriptions.Item>
                                                <Descriptions.Item label="运行状态">
                                                    <Text type="success">正常运行</Text>
                                                </Descriptions.Item>
                                            </Descriptions>
                                        </Card>
                                    </Col>
                                    <Col span={12}>
                                        <Card title="数据统计" size="small">
                                            <Descriptions column={1}>
                                                <Descriptions.Item label="用户总数">{systemInfo.total_users}</Descriptions.Item>
                                                <Descriptions.Item label="客户总数">{systemInfo.total_customers}</Descriptions.Item>
                                                <Descriptions.Item label="自定义字段">{systemInfo.total_custom_fields}</Descriptions.Item>
                                                <Descriptions.Item label="最后备份">{systemInfo.last_backup || '无'}</Descriptions.Item>
                                            </Descriptions>
                                        </Card>
                                    </Col>
                                </Row>
                            </TabPane>
                        </Tabs>
                    </Card>
                </div>
            );
        };

        // Menu Management Component
        const MenuManagement = () => {
            const [menus, setMenus] = useState([]);
            const [loading, setLoading] = useState(false);
            const [modalVisible, setModalVisible] = useState(false);
            const [editingMenu, setEditingMenu] = useState(null);
            const [form] = Form.useForm();

            useEffect(() => {
                loadMenus();
            }, []);

            const loadMenus = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/menus');
                    setMenus(response.menus || []);
                } catch (error) {
                    message.error('加载菜单列表失败');
                } finally {
                    setLoading(false);
                }
            };

            const handleAdd = () => {
                setEditingMenu(null);
                form.resetFields();
                setModalVisible(true);
            };

            const handleEdit = (menu) => {
                setEditingMenu(menu);
                form.setFieldsValue(menu);
                setModalVisible(true);
            };

            const handleDelete = async (id) => {
                try {
                    await api.delete(`/menus/${id}`);
                    message.success('删除成功');
                    loadMenus();
                } catch (error) {
                    message.error('删除失败：' + (error.response?.data?.detail || '网络错误'));
                }
            };

            const handleSubmit = async (values) => {
                try {
                    if (editingMenu) {
                        await api.put(`/menus/${editingMenu.id}`, values);
                        message.success('更新成功');
                    } else {
                        await api.post('/menus', values);
                        message.success('创建成功');
                    }
                    setModalVisible(false);
                    loadMenus();
                } catch (error) {
                    message.error(editingMenu ? '更新失败' : '创建失败');
                }
            };

            const renderMenuTree = (menuList, level = 0) => {
                return menuList.map(menu => (
                    <div key={menu.id} style={{ marginLeft: level * 20, marginBottom: 8 }}>
                        <Card size="small" style={{ backgroundColor: level > 0 ? '#f9f9f9' : 'white' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <div>
                                    <Text strong>{menu.label}</Text>
                                    <Text type="secondary" style={{ marginLeft: 8 }}>({menu.name})</Text>
                                    {menu.icon && <Text type="secondary" style={{ marginLeft: 8 }}>{menu.icon}</Text>}
                                </div>
                                <Space>
                                    <Button size="small" onClick={() => handleEdit(menu)}>编辑</Button>
                                    <Button
                                        size="small"
                                        danger
                                        onClick={() => {
                                            Modal.confirm({
                                                title: '确认删除',
                                                content: `确定要删除菜单 "${menu.label}" 吗？`,
                                                onOk: () => handleDelete(menu.id),
                                            });
                                        }}
                                    >
                                        删除
                                    </Button>
                                </Space>
                            </div>
                        </Card>
                        {menu.children && menu.children.length > 0 && renderMenuTree(menu.children, level + 1)}
                    </div>
                ));
            };

            return (
                <div>
                    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>📋 菜单管理</Title>
                            <Paragraph type="secondary">
                                管理系统菜单结构和导航配置
                            </Paragraph>
                        </div>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size="large"
                        >
                            新建菜单
                        </Button>
                    </div>

                    <Card loading={loading}>
                        {menus.length > 0 ? renderMenuTree(menus) : (
                            <div style={{ textAlign: 'center', padding: '40px 0' }}>
                                <Text type="secondary">暂无菜单数据</Text>
                            </div>
                        )}
                    </Card>

                    <Modal
                        title={editingMenu ? '编辑菜单' : '新建菜单'}
                        visible={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        onOk={() => form.submit()}
                        width={600}
                    >
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleSubmit}
                        >
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="name"
                                        label="菜单名称"
                                        rules={[{ required: true, message: '请输入菜单名称' }]}
                                    >
                                        <Input placeholder="例如: dashboard" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="label"
                                        label="显示标签"
                                        rules={[{ required: true, message: '请输入显示标签' }]}
                                    >
                                        <Input placeholder="例如: 仪表板" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item name="icon" label="图标">
                                        <Input placeholder="例如: DashboardOutlined" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item name="path" label="路径">
                                        <Input placeholder="例如: /dashboard" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item name="component" label="组件">
                                        <Input placeholder="例如: Dashboard" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item name="sort_order" label="排序">
                                        <Input type="number" placeholder="数字越小越靠前" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Form.Item name="parent_id" label="父级菜单">
                                <Select placeholder="选择父级菜单（可选）" allowClear>
                                    {menus.filter(menu => menu.id !== editingMenu?.id).map(menu => (
                                        <Select.Option key={menu.id} value={menu.id}>
                                            {menu.label}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item name="is_active" label="是否启用" valuePropName="checked">
                                        <input type="checkbox" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item name="is_visible" label="是否可见" valuePropName="checked">
                                        <input type="checkbox" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Form.Item name="permission" label="权限标识">
                                <Input placeholder="例如: admin" />
                            </Form.Item>

                            <Form.Item name="description" label="描述">
                                <Input.TextArea placeholder="菜单描述" rows={2} />
                            </Form.Item>
                        </Form>
                    </Modal>
                </div>
            );
        };

        // Form Builder Component
        const FormBuilder = () => {
            const [templates, setTemplates] = useState([]);
            const [loading, setLoading] = useState(false);
            const [modalVisible, setModalVisible] = useState(false);
            const [editingTemplate, setEditingTemplate] = useState(null);
            const [form] = Form.useForm();
            const [formFields, setFormFields] = useState([]);

            useEffect(() => {
                loadTemplates();
            }, []);

            const loadTemplates = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/menus/forms');
                    setTemplates(response.templates || []);
                } catch (error) {
                    message.error('加载表单模板失败');
                } finally {
                    setLoading(false);
                }
            };

            const handleAdd = () => {
                setEditingTemplate(null);
                setFormFields([]);
                form.resetFields();
                setModalVisible(true);
            };

            const handleEdit = (template) => {
                setEditingTemplate(template);
                try {
                    const config = JSON.parse(template.form_config);
                    setFormFields(config.fields || []);
                    form.setFieldsValue({
                        name: template.name,
                        title: template.title,
                        description: template.description,
                        entity_type: template.entity_type
                    });
                } catch (error) {
                    message.error('表单配置解析失败');
                }
                setModalVisible(true);
            };

            const addField = () => {
                const newField = {
                    id: Date.now(),
                    name: '',
                    label: '',
                    type: 'text',
                    required: false,
                    placeholder: '',
                    options: []
                };
                setFormFields([...formFields, newField]);
            };

            const updateField = (index, field) => {
                const newFields = [...formFields];
                newFields[index] = field;
                setFormFields(newFields);
            };

            const removeField = (index) => {
                const newFields = formFields.filter((_, i) => i !== index);
                setFormFields(newFields);
            };

            const handleSubmit = async (values) => {
                try {
                    const formConfig = {
                        fields: formFields,
                        layout: 'vertical'
                    };

                    const submitData = {
                        ...values,
                        form_config: JSON.stringify(formConfig)
                    };

                    if (editingTemplate) {
                        await api.put(`/menus/forms/${editingTemplate.id}`, submitData);
                        message.success('更新成功');
                    } else {
                        await api.post('/menus/forms', submitData);
                        message.success('创建成功');
                    }
                    setModalVisible(false);
                    loadTemplates();
                } catch (error) {
                    message.error(editingTemplate ? '更新失败' : '创建失败');
                }
            };

            const columns = [
                {
                    title: '表单名称',
                    dataIndex: 'name',
                    key: 'name',
                },
                {
                    title: '表单标题',
                    dataIndex: 'title',
                    key: 'title',
                },
                {
                    title: '实体类型',
                    dataIndex: 'entity_type',
                    key: 'entity_type',
                },
                {
                    title: '状态',
                    dataIndex: 'is_active',
                    key: 'is_active',
                    render: (active) => (
                        <Text type={active ? 'success' : 'secondary'}>
                            {active ? '启用' : '禁用'}
                        </Text>
                    ),
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                        <Space size="middle">
                            <Button type="link" onClick={() => handleEdit(record)}>
                                编辑
                            </Button>
                            <Button
                                type="link"
                                danger
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认删除',
                                        content: `确定要删除表单 "${record.title}" 吗？`,
                                        onOk: async () => {
                                            try {
                                                await api.delete(`/menus/forms/${record.id}`);
                                                message.success('删除成功');
                                                loadTemplates();
                                            } catch (error) {
                                                message.error('删除失败');
                                            }
                                        },
                                    });
                                }}
                            >
                                删除
                            </Button>
                        </Space>
                    ),
                },
            ];

            return (
                <div>
                    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>📝 表单构建器</Title>
                            <Paragraph type="secondary">
                                创建和管理动态表单模板
                            </Paragraph>
                        </div>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size="large"
                        >
                            新建表单
                        </Button>
                    </div>

                    <Card>
                        <Table
                            columns={columns}
                            dataSource={templates}
                            loading={loading}
                            rowKey="id"
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条记录`,
                            }}
                        />
                    </Card>

                    <Modal
                        title={editingTemplate ? '编辑表单模板' : '新建表单模板'}
                        visible={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        onOk={() => form.submit()}
                        width={800}
                    >
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleSubmit}
                        >
                            <Row gutter={16}>
                                <Col span={12}>
                                    <Form.Item
                                        name="name"
                                        label="表单名称"
                                        rules={[{ required: true, message: '请输入表单名称' }]}
                                    >
                                        <Input placeholder="例如: customer_form" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="title"
                                        label="表单标题"
                                        rules={[{ required: true, message: '请输入表单标题' }]}
                                    >
                                        <Input placeholder="例如: 客户信息表单" />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Form.Item name="description" label="表单描述">
                                <Input.TextArea placeholder="表单描述" rows={2} />
                            </Form.Item>

                            <Form.Item name="entity_type" label="实体类型">
                                <Select placeholder="选择实体类型">
                                    <Select.Option value="customer">客户</Select.Option>
                                    <Select.Option value="order">订单</Select.Option>
                                    <Select.Option value="custom">自定义</Select.Option>
                                </Select>
                            </Form.Item>

                            <div style={{ marginBottom: 16 }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                                    <Text strong>表单字段</Text>
                                    <Button type="dashed" onClick={addField} icon={<PlusOutlined />}>
                                        添加字段
                                    </Button>
                                </div>

                                {formFields.map((field, index) => (
                                    <Card key={field.id} size="small" style={{ marginBottom: 8 }}>
                                        <Row gutter={8} align="middle">
                                            <Col span={4}>
                                                <Input
                                                    placeholder="字段名"
                                                    value={field.name}
                                                    onChange={(e) => updateField(index, { ...field, name: e.target.value })}
                                                />
                                            </Col>
                                            <Col span={4}>
                                                <Input
                                                    placeholder="显示标签"
                                                    value={field.label}
                                                    onChange={(e) => updateField(index, { ...field, label: e.target.value })}
                                                />
                                            </Col>
                                            <Col span={3}>
                                                <Select
                                                    value={field.type}
                                                    onChange={(value) => updateField(index, { ...field, type: value })}
                                                    style={{ width: '100%' }}
                                                >
                                                    <Select.Option value="text">文本</Select.Option>
                                                    <Select.Option value="number">数字</Select.Option>
                                                    <Select.Option value="email">邮箱</Select.Option>
                                                    <Select.Option value="phone">电话</Select.Option>
                                                    <Select.Option value="textarea">多行文本</Select.Option>
                                                    <Select.Option value="select">下拉选择</Select.Option>
                                                    <Select.Option value="radio">单选框</Select.Option>
                                                    <Select.Option value="checkbox">复选框</Select.Option>
                                                    <Select.Option value="date">日期</Select.Option>
                                                    <Select.Option value="datetime">日期时间</Select.Option>
                                                    <Select.Option value="file">文件上传</Select.Option>
                                                </Select>
                                            </Col>
                                            <Col span={4}>
                                                <Input
                                                    placeholder="占位符"
                                                    value={field.placeholder}
                                                    onChange={(e) => updateField(index, { ...field, placeholder: e.target.value })}
                                                />
                                            </Col>
                                            <Col span={2}>
                                                <input
                                                    type="checkbox"
                                                    checked={field.required}
                                                    onChange={(e) => updateField(index, { ...field, required: e.target.checked })}
                                                />
                                                <span style={{ marginLeft: 4 }}>必填</span>
                                            </Col>
                                            <Col span={2}>
                                                <Button
                                                    type="text"
                                                    danger
                                                    onClick={() => removeField(index)}
                                                    icon={<DeleteOutlined />}
                                                />
                                            </Col>
                                        </Row>

                                        {/* 选项配置（针对select、radio、checkbox类型） */}
                                        {['select', 'radio', 'checkbox'].includes(field.type) && (
                                            <div style={{ marginTop: 8, paddingLeft: 16 }}>
                                                <Text type="secondary" style={{ fontSize: '12px' }}>选项配置（每行一个选项）：</Text>
                                                <Input.TextArea
                                                    placeholder="选项1&#10;选项2&#10;选项3"
                                                    rows={3}
                                                    value={field.options ? field.options.join('\n') : ''}
                                                    onChange={(e) => {
                                                        const options = e.target.value.split('\n').filter(opt => opt.trim());
                                                        updateField(index, { ...field, options });
                                                    }}
                                                    style={{ marginTop: 4 }}
                                                />
                                            </div>
                                        )}

                                        {/* 验证规则配置 */}
                                        <div style={{ marginTop: 8, paddingLeft: 16 }}>
                                            <Row gutter={8}>
                                                {field.type === 'text' && (
                                                    <>
                                                        <Col span={6}>
                                                            <Text type="secondary" style={{ fontSize: '12px' }}>最小长度：</Text>
                                                            <Input
                                                                type="number"
                                                                placeholder="0"
                                                                value={field.minLength || ''}
                                                                onChange={(e) => updateField(index, { ...field, minLength: e.target.value })}
                                                                size="small"
                                                            />
                                                        </Col>
                                                        <Col span={6}>
                                                            <Text type="secondary" style={{ fontSize: '12px' }}>最大长度：</Text>
                                                            <Input
                                                                type="number"
                                                                placeholder="100"
                                                                value={field.maxLength || ''}
                                                                onChange={(e) => updateField(index, { ...field, maxLength: e.target.value })}
                                                                size="small"
                                                            />
                                                        </Col>
                                                    </>
                                                )}
                                                {field.type === 'number' && (
                                                    <>
                                                        <Col span={6}>
                                                            <Text type="secondary" style={{ fontSize: '12px' }}>最小值：</Text>
                                                            <Input
                                                                type="number"
                                                                placeholder="0"
                                                                value={field.min || ''}
                                                                onChange={(e) => updateField(index, { ...field, min: e.target.value })}
                                                                size="small"
                                                            />
                                                        </Col>
                                                        <Col span={6}>
                                                            <Text type="secondary" style={{ fontSize: '12px' }}>最大值：</Text>
                                                            <Input
                                                                type="number"
                                                                placeholder="999999"
                                                                value={field.max || ''}
                                                                onChange={(e) => updateField(index, { ...field, max: e.target.value })}
                                                                size="small"
                                                            />
                                                        </Col>
                                                    </>
                                                )}
                                                <Col span={6}>
                                                    <Text type="secondary" style={{ fontSize: '12px' }}>默认值：</Text>
                                                    <Input
                                                        placeholder="默认值"
                                                        value={field.defaultValue || ''}
                                                        onChange={(e) => updateField(index, { ...field, defaultValue: e.target.value })}
                                                        size="small"
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </Form>
                    </Modal>
                </div>
            );
        };

        // Chart Builder Component
        const ChartBuilder = () => {
            const [charts, setCharts] = useState([]);
            const [loading, setLoading] = useState(false);
            const [modalVisible, setModalVisible] = useState(false);
            const [editingChart, setEditingChart] = useState(null);
            const [form] = Form.useForm();
            const [chartConfig, setChartConfig] = useState({
                type: 'bar',
                dataSource: 'customers',
                xField: '',
                yField: '',
                title: '',
                description: ''
            });

            useEffect(() => {
                loadCharts();
            }, []);

            const loadCharts = async () => {
                try {
                    setLoading(true);
                    const response = await api.get('/charts');
                    setCharts(response.charts || []);
                } catch (error) {
                    message.error('加载图表列表失败');
                } finally {
                    setLoading(false);
                }
            };

            const handleAdd = () => {
                setEditingChart(null);
                setChartConfig({
                    type: 'bar',
                    dataSource: 'customers',
                    xField: '',
                    yField: '',
                    title: '',
                    description: ''
                });
                form.resetFields();
                setModalVisible(true);
            };

            const handleEdit = (chart) => {
                setEditingChart(chart);
                try {
                    const config = JSON.parse(chart.chart_config || '{}');
                    setChartConfig(config);
                    form.setFieldsValue({
                        name: chart.name,
                        title: chart.title,
                        description: chart.description,
                        chart_type: chart.chart_type,
                        data_source: chart.data_source,
                        x_field: chart.x_field,
                        y_field: chart.y_field
                    });
                } catch (error) {
                    message.error('图表配置解析失败');
                }
                setModalVisible(true);
            };

            const generateMockData = () => {
                const { type, dataSource } = chartConfig;

                if (dataSource === 'customers') {
                    return [
                        { month: '1月', count: 12, revenue: 15000 },
                        { month: '2月', count: 18, revenue: 22000 },
                        { month: '3月', count: 25, revenue: 31000 },
                        { month: '4月', count: 20, revenue: 28000 },
                        { month: '5月', count: 30, revenue: 35000 },
                        { month: '6月', count: 28, revenue: 33000 }
                    ];
                }
                return [];
            };

            const renderChart = () => {
                const data = generateMockData();
                const { type, xField, yField } = chartConfig;

                if (!xField || !yField || data.length === 0) {
                    return (
                        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                            请配置图表字段以预览效果
                        </div>
                    );
                }

                // 简单的图表渲染（实际项目中可以使用 ECharts 或 Chart.js）
                return (
                    <div style={{ padding: '20px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                        <h4>{chartConfig.title || '图表预览'}</h4>
                        <div style={{ display: 'flex', alignItems: 'end', height: '200px', gap: '10px' }}>
                            {data.map((item, index) => (
                                <div key={index} style={{ textAlign: 'center', flex: 1 }}>
                                    <div
                                        style={{
                                            height: `${(item[yField] / Math.max(...data.map(d => d[yField]))) * 150}px`,
                                            backgroundColor: '#1890ff',
                                            marginBottom: '5px',
                                            borderRadius: '2px'
                                        }}
                                    />
                                    <div style={{ fontSize: '12px' }}>{item[xField]}</div>
                                    <div style={{ fontSize: '10px', color: '#666' }}>{item[yField]}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                );
            };

            const handleSubmit = async (values) => {
                try {
                    if (editingChart) {
                        await api.put(`/charts/${editingChart.id}`, values);
                        message.success('图表更新成功');
                    } else {
                        await api.post('/charts', values);
                        message.success('图表创建成功');
                    }
                    setModalVisible(false);
                    loadCharts();
                } catch (error) {
                    message.error(editingChart ? '更新失败' : '创建失败');
                }
            };

            const columns = [
                {
                    title: '图表名称',
                    dataIndex: 'name',
                    key: 'name',
                },
                {
                    title: '图表标题',
                    dataIndex: 'title',
                    key: 'title',
                },
                {
                    title: '图表类型',
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => {
                        const typeMap = {
                            'bar': '柱状图',
                            'line': '折线图',
                            'pie': '饼图',
                            'area': '面积图'
                        };
                        return typeMap[type] || type;
                    },
                },
                {
                    title: '数据源',
                    dataIndex: 'dataSource',
                    key: 'dataSource',
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                        <Space size="middle">
                            <Button type="link" onClick={() => handleEdit(record)}>
                                编辑
                            </Button>
                            <Button type="link" onClick={() => {
                                Modal.info({
                                    title: `预览图表: ${record.title}`,
                                    width: 800,
                                    content: (
                                        <div style={{ marginTop: 16 }}>
                                            <ChartWidget
                                                chartConfig={JSON.parse(record.chart_config || '{}')}
                                                data={generateMockData()}
                                            />
                                        </div>
                                    ),
                                });
                            }}>
                                预览
                            </Button>
                            <Button
                                type="link"
                                danger
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认删除',
                                        content: `确定要删除图表 "${record.title}" 吗？`,
                                        onOk: () => {
                                            message.success('删除成功');
                                            loadCharts();
                                        },
                                    });
                                }}
                            >
                                删除
                            </Button>
                        </Space>
                    ),
                },
            ];

            return (
                <div>
                    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2}>📊 图表构建器</Title>
                            <Paragraph type="secondary">
                                创建和管理自定义数据图表
                            </Paragraph>
                        </div>
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={handleAdd}
                            size="large"
                        >
                            新建图表
                        </Button>
                    </div>

                    <Card>
                        <Table
                            columns={columns}
                            dataSource={charts}
                            loading={loading}
                            rowKey="id"
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条记录`,
                            }}
                        />
                    </Card>

                    <Modal
                        title={editingChart ? '编辑图表' : '新建图表'}
                        visible={modalVisible}
                        onCancel={() => setModalVisible(false)}
                        onOk={() => form.submit()}
                        width={1000}
                    >
                        <Row gutter={24}>
                            <Col span={12}>
                                <Form
                                    form={form}
                                    layout="vertical"
                                    onFinish={handleSubmit}
                                    onValuesChange={(changedValues, allValues) => {
                                        setChartConfig({ ...chartConfig, ...changedValues });
                                    }}
                                >
                                    <Form.Item
                                        name="name"
                                        label="图表名称"
                                        rules={[{ required: true, message: '请输入图表名称' }]}
                                    >
                                        <Input placeholder="例如: customer_stats" />
                                    </Form.Item>

                                    <Form.Item
                                        name="title"
                                        label="图表标题"
                                        rules={[{ required: true, message: '请输入图表标题' }]}
                                    >
                                        <Input placeholder="例如: 客户统计图表" />
                                    </Form.Item>

                                    <Form.Item
                                        name="chart_type"
                                        label="图表类型"
                                        rules={[{ required: true, message: '请选择图表类型' }]}
                                    >
                                        <Select placeholder="选择图表类型">
                                            <Select.Option value="bar">柱状图</Select.Option>
                                            <Select.Option value="line">折线图</Select.Option>
                                            <Select.Option value="pie">饼图</Select.Option>
                                            <Select.Option value="area">面积图</Select.Option>
                                        </Select>
                                    </Form.Item>

                                    <Form.Item
                                        name="data_source"
                                        label="数据源"
                                        rules={[{ required: true, message: '请选择数据源' }]}
                                    >
                                        <Select placeholder="选择数据源">
                                            <Select.Option value="customers">客户数据</Select.Option>
                                            <Select.Option value="orders">订单数据</Select.Option>
                                            <Select.Option value="revenue">收入数据</Select.Option>
                                            <Select.Option value="custom">自定义数据</Select.Option>
                                        </Select>
                                    </Form.Item>

                                    <Row gutter={16}>
                                        <Col span={12}>
                                            <Form.Item
                                                name="x_field"
                                                label="X轴字段"
                                                rules={[{ required: true, message: '请输入X轴字段' }]}
                                            >
                                                <Select placeholder="选择X轴字段">
                                                    <Select.Option value="month">月份</Select.Option>
                                                    <Select.Option value="date">日期</Select.Option>
                                                    <Select.Option value="category">分类</Select.Option>
                                                    <Select.Option value="company_type">公司类型</Select.Option>
                                                </Select>
                                            </Form.Item>
                                        </Col>
                                        <Col span={12}>
                                            <Form.Item
                                                name="y_field"
                                                label="Y轴字段"
                                                rules={[{ required: true, message: '请输入Y轴字段' }]}
                                            >
                                                <Select placeholder="选择Y轴字段">
                                                    <Select.Option value="count">数量</Select.Option>
                                                    <Select.Option value="revenue">收入</Select.Option>
                                                    <Select.Option value="amount">金额</Select.Option>
                                                    <Select.Option value="value">值</Select.Option>
                                                </Select>
                                            </Form.Item>
                                        </Col>
                                    </Row>

                                    <Form.Item name="description" label="图表描述">
                                        <Input.TextArea placeholder="图表描述" rows={3} />
                                    </Form.Item>

                                    <Form.Item name="is_public" label="是否公开" valuePropName="checked">
                                        <input type="checkbox" />
                                        <span style={{ marginLeft: 8 }}>允许其他用户查看此图表</span>
                                    </Form.Item>
                                </Form>
                            </Col>
                            <Col span={12}>
                                <div>
                                    <Title level={4}>图表预览</Title>
                                    {renderChart()}
                                </div>
                            </Col>
                        </Row>
                    </Modal>
                </div>
            );
        };

        // Main Layout Component
        const MainLayout = ({ children }) => {
            const [collapsed, setCollapsed] = useState(false);
            const [currentPage, setCurrentPage] = useState('dashboard');
            const { logout } = useAuth();

            const menuItems = [
                {
                    key: 'dashboard',
                    icon: <DashboardOutlined />,
                    label: '仪表板',
                },
                {
                    key: 'customers',
                    icon: <TeamOutlined />,
                    label: '客户管理',
                },
                {
                    key: 'custom-fields',
                    icon: <SettingOutlined />,
                    label: '字段配置',
                },
                {
                    key: 'menu-management',
                    icon: <SettingOutlined />,
                    label: '菜单管理',
                },
                {
                    key: 'form-builder',
                    icon: <SettingOutlined />,
                    label: '表单构建',
                },
                {
                    key: 'chart-builder',
                    icon: <SettingOutlined />,
                    label: '图表构建',
                },
                {
                    key: 'settings',
                    icon: <SettingOutlined />,
                    label: '系统设置',
                },
            ];

            const userMenuItems = [
                {
                    key: 'profile',
                    icon: <UserOutlined />,
                    label: '个人资料',
                    onClick: () => setCurrentPage('settings'),
                },
                {
                    key: 'settings',
                    icon: <SettingOutlined />,
                    label: '系统设置',
                    onClick: () => setCurrentPage('settings'),
                },
                {
                    type: 'divider',
                },
                {
                    key: 'logout',
                    icon: <LogoutOutlined />,
                    label: '退出登录',
                    onClick: logout,
                },
            ];

            const handleMenuClick = ({ key }) => {
                setCurrentPage(key);
            };

            const renderContent = () => {
                switch (currentPage) {
                    case 'dashboard':
                        return <Dashboard onNavigate={setCurrentPage} />;
                    case 'customers':
                        return <Customers />;
                    case 'custom-fields':
                        return <CustomFields />;
                    case 'menu-management':
                        return <MenuManagement />;
                    case 'form-builder':
                        return <FormBuilder />;
                    case 'chart-builder':
                        return <ChartBuilder />;
                    case 'settings':
                        return <Settings />;
                    default:
                        return <Dashboard onNavigate={setCurrentPage} />;
                }
            };

            return (
                <Layout style={{ minHeight: '100vh' }}>
                    <Sider trigger={null} collapsible collapsed={collapsed} className="sidebar">
                        <div style={{
                            height: 64,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderBottom: '1px solid #f0f0f0'
                        }}>
                            <Title level={4} style={{ color: 'white', margin: 0 }}>
                                {collapsed ? 'CRM' : 'PyCRM'}
                            </Title>
                        </div>
                        <Menu
                            theme="dark"
                            mode="inline"
                            selectedKeys={[currentPage]}
                            items={menuItems}
                            onClick={handleMenuClick}
                        />
                    </Sider>

                    <Layout>
                        <Header style={{
                            padding: '0 16px',
                            background: '#fff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            borderBottom: '1px solid #f0f0f0'
                        }}>
                            <Button
                                type="text"
                                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                                onClick={() => setCollapsed(!collapsed)}
                                style={{ fontSize: '16px', width: 64, height: 64 }}
                            />

                            <Space>
                                <Button type="primary" icon={<PlusOutlined />}>
                                    新建客户
                                </Button>

                                <Dropdown
                                    menu={{ items: userMenuItems }}
                                    placement="bottomRight"
                                >
                                    <Space style={{ cursor: 'pointer' }}>
                                        <Avatar icon={<UserOutlined />} />
                                        <span>管理员</span>
                                    </Space>
                                </Dropdown>
                            </Space>
                        </Header>

                        <Content style={{
                            margin: '16px',
                            padding: '24px',
                            background: '#fff',
                            borderRadius: '6px',
                            overflow: 'auto'
                        }}>
                            {renderContent()}
                        </Content>
                    </Layout>
                </Layout>
            );
        };

        // Main App Component
        const App = () => {
            const { user } = useAuth();

            return (
                <div>
                    {user ? <MainLayout /> : <Login />}
                </div>
            );
        };

        // Render App
        ReactDOM.render(
            <AuthProvider>
                <App />
            </AuthProvider>,
            document.getElementById('root')
        );
    </script>
</body>
</html>
