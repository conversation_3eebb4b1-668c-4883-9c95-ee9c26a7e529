from sqlalchemy.orm import Session
from app.db.database import SessionLocal
from app.models.user import User
from app.models.customer import Customer
from app.models.custom_field import CustomField, CustomFieldValue, FieldType
from app.models.menu import Menu, FormTemplate
from app.models.chart import Chart
from app.core.security import get_password_hash
from datetime import datetime, timedelta
import json
import random

def create_demo_data():
    """创建演示数据"""
    db = SessionLocal()

    try:
        print("🎯 开始创建演示数据...")

        # 创建演示客户数据
        create_demo_customers(db)

        # 创建自定义字段
        create_demo_custom_fields(db)

        # 创建演示图表
        create_demo_charts(db)

        # 创建表单模板
        create_demo_form_templates(db)

        print("✅ 演示数据创建完成！")

    except Exception as e:
        print(f"❌ 创建演示数据失败: {e}")
        db.rollback()
    finally:
        db.close()

def create_demo_customers(db: Session):
    """创建演示客户数据"""
    # 检查是否已有客户数据
    existing_customers = db.query(Customer).count()
    if existing_customers > 0:
        print("📋 客户数据已存在，跳过创建")
        return

    # 演示客户数据
    demo_customers = [
        {
            "name": "张三",
            "email": "<EMAIL>",
            "phone": "13800138001",
            "company": "阿里巴巴集团",
            "address": "杭州市西湖区文三路969号",
            "notes": "重要客户，年采购额超过100万"
        },
        {
            "name": "李四",
            "email": "<EMAIL>",
            "phone": "13800138002",
            "company": "腾讯科技",
            "address": "深圳市南山区科技园",
            "notes": "技术合作伙伴"
        },
        {
            "name": "王五",
            "email": "<EMAIL>",
            "phone": "13800138003",
            "company": "百度在线",
            "address": "北京市海淀区上地十街10号",
            "notes": "潜在大客户"
        },
        {
            "name": "赵六",
            "email": "<EMAIL>",
            "phone": "13800138004",
            "company": "字节跳动",
            "address": "北京市海淀区知春路63号",
            "notes": "新兴客户，发展潜力大"
        },
        {
            "name": "钱七",
            "email": "<EMAIL>",
            "phone": "13800138005",
            "company": "美团点评",
            "address": "北京市朝阳区望京东路6号",
            "notes": "服务行业客户"
        },
        {
            "name": "孙八",
            "email": "<EMAIL>",
            "phone": "13800138006",
            "company": "滴滴出行",
            "address": "北京市海淀区中关村软件园",
            "notes": "出行领域合作伙伴"
        },
        {
            "name": "周九",
            "email": "<EMAIL>",
            "phone": "13800138007",
            "company": "京东集团",
            "address": "北京市大兴区京东总部",
            "notes": "电商平台客户"
        },
        {
            "name": "吴十",
            "email": "<EMAIL>",
            "phone": "13800138008",
            "company": "网易公司",
            "address": "杭州市滨江区网商路599号",
            "notes": "游戏娱乐客户"
        }
    ]

    # 创建客户，分布在过去6个月
    base_date = datetime.now() - timedelta(days=180)

    for i, customer_data in enumerate(demo_customers):
        # 随机分布创建时间
        created_at = base_date + timedelta(days=random.randint(0, 180))

        customer = Customer(
            **customer_data,
            created_at=created_at
        )
        db.add(customer)

    db.commit()
    print(f"📋 创建了 {len(demo_customers)} 个演示客户")

def create_demo_custom_fields(db: Session):
    """创建演示自定义字段"""
    # 检查是否已有自定义字段
    existing_fields = db.query(CustomField).count()
    if existing_fields > 0:
        print("🔧 自定义字段已存在，跳过创建")
        return

    demo_fields = [
        {
            "name": "customer_level",
            "label": "客户等级",
            "field_type": FieldType.SELECT,
            "entity_type": "customer",
            "is_required": "false",
            "options": json.dumps(["普通客户", "重要客户", "VIP客户", "战略客户"])
        },
        {
            "name": "industry",
            "label": "所属行业",
            "field_type": FieldType.SELECT,
            "entity_type": "customer",
            "is_required": "false",
            "options": json.dumps(["互联网", "金融", "制造业", "教育", "医疗", "零售", "其他"])
        },
        {
            "name": "annual_revenue",
            "label": "年营收",
            "field_type": FieldType.NUMBER,
            "entity_type": "customer",
            "is_required": "false"
        },
        {
            "name": "contact_person",
            "label": "联系人",
            "field_type": FieldType.TEXT,
            "entity_type": "customer",
            "is_required": "false"
        },
        {
            "name": "last_contact_date",
            "label": "最后联系日期",
            "field_type": FieldType.DATE,
            "entity_type": "customer",
            "is_required": "false"
        }
    ]

    for field_data in demo_fields:
        field = CustomField(**field_data)
        db.add(field)

    db.commit()
    print(f"🔧 创建了 {len(demo_fields)} 个自定义字段")

def create_demo_charts(db: Session):
    """创建演示图表"""
    # 检查是否已有图表
    existing_charts = db.query(Chart).count()
    if existing_charts > 0:
        print("📊 图表已存在，跳过创建")
        return

    # 获取admin用户ID
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        print("❌ 未找到admin用户，跳过图表创建")
        return

    demo_charts = [
        {
            "name": "customer_growth",
            "title": "客户增长趋势",
            "description": "显示过去6个月的客户增长情况",
            "chart_type": "line",
            "data_source": "customers",
            "x_field": "month",
            "y_field": "count",
            "chart_config": json.dumps({
                "type": "line",
                "xField": "month",
                "yField": "count",
                "title": "客户增长趋势"
            }),
            "is_public": True,
            "created_by": admin_user.id
        },
        {
            "name": "customer_by_company",
            "title": "客户公司分布",
            "description": "按公司类型分布的客户数量",
            "chart_type": "pie",
            "data_source": "customers",
            "x_field": "company_type",
            "y_field": "count",
            "chart_config": json.dumps({
                "type": "pie",
                "xField": "company_type",
                "yField": "count",
                "title": "客户公司分布"
            }),
            "is_public": True,
            "created_by": admin_user.id
        },
        {
            "name": "monthly_revenue",
            "title": "月度收入统计",
            "description": "显示每月的收入情况",
            "chart_type": "bar",
            "data_source": "revenue",
            "x_field": "month",
            "y_field": "amount",
            "chart_config": json.dumps({
                "type": "bar",
                "xField": "month",
                "yField": "amount",
                "title": "月度收入统计"
            }),
            "is_public": True,
            "created_by": admin_user.id
        }
    ]

    for chart_data in demo_charts:
        chart = Chart(**chart_data)
        db.add(chart)

    db.commit()
    print(f"📊 创建了 {len(demo_charts)} 个演示图表")

def create_demo_form_templates(db: Session):
    """创建演示表单模板"""
    # 检查是否已有表单模板
    existing_templates = db.query(FormTemplate).count()
    if existing_templates > 0:
        print("📝 表单模板已存在，跳过创建")
        return

    # 获取admin用户ID
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        print("❌ 未找到admin用户，跳过表单模板创建")
        return

    demo_templates = [
        {
            "name": "customer_feedback",
            "title": "客户反馈表",
            "description": "收集客户对产品和服务的反馈",
            "entity_type": "feedback",
            "form_config": json.dumps({
                "fields": [
                    {
                        "name": "customer_name",
                        "label": "客户姓名",
                        "type": "text",
                        "required": True,
                        "placeholder": "请输入客户姓名"
                    },
                    {
                        "name": "satisfaction",
                        "label": "满意度",
                        "type": "select",
                        "required": True,
                        "options": ["非常满意", "满意", "一般", "不满意", "非常不满意"]
                    },
                    {
                        "name": "feedback_content",
                        "label": "反馈内容",
                        "type": "textarea",
                        "required": True,
                        "placeholder": "请详细描述您的反馈"
                    }
                ],
                "layout": "vertical"
            }),
            "created_by": admin_user.id
        },
        {
            "name": "sales_lead",
            "title": "销售线索表",
            "description": "记录潜在客户信息",
            "entity_type": "lead",
            "form_config": json.dumps({
                "fields": [
                    {
                        "name": "lead_name",
                        "label": "线索名称",
                        "type": "text",
                        "required": True
                    },
                    {
                        "name": "company",
                        "label": "公司名称",
                        "type": "text",
                        "required": True
                    },
                    {
                        "name": "contact_phone",
                        "label": "联系电话",
                        "type": "text",
                        "required": True
                    },
                    {
                        "name": "lead_source",
                        "label": "线索来源",
                        "type": "select",
                        "required": True,
                        "options": ["网站咨询", "电话咨询", "展会", "推荐", "其他"]
                    },
                    {
                        "name": "budget",
                        "label": "预算范围",
                        "type": "select",
                        "required": False,
                        "options": ["10万以下", "10-50万", "50-100万", "100万以上"]
                    }
                ],
                "layout": "vertical"
            }),
            "created_by": admin_user.id
        }
    ]

    for template_data in demo_templates:
        template = FormTemplate(**template_data)
        db.add(template)

    db.commit()
    print(f"📝 创建了 {len(demo_templates)} 个表单模板")

if __name__ == "__main__":
    create_demo_data()
