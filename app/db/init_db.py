from sqlalchemy.orm import Session
from app.db.database import engine, SessionLocal, Base
from app.models.user import User
from app.models.customer import Customer
from app.models.contact import Contact
from app.models.opportunity import Opportunity
from app.models.custom_field import CustomField, CustomFieldValue
from app.models.menu import Menu, FormTemplate, FormData
from app.models.chart import Chart, Dashboard, DashboardChart
from app.core.security import get_password_hash

def init_default_menus(db):
    """初始化默认菜单"""
    # 检查是否已有菜单
    existing_menus = db.query(Menu).count()
    if existing_menus > 0:
        return

    # 创建默认菜单
    default_menus = [
        {
            "name": "dashboard",
            "label": "仪表板",
            "icon": "DashboardOutlined",
            "path": "/dashboard",
            "component": "Dashboard",
            "sort_order": 1
        },
        {
            "name": "customers",
            "label": "客户管理",
            "icon": "TeamOutlined",
            "path": "/customers",
            "component": "Customers",
            "sort_order": 2
        },
        {
            "name": "custom-fields",
            "label": "字段配置",
            "icon": "SettingOutlined",
            "path": "/custom-fields",
            "component": "CustomFields",
            "sort_order": 3
        },
        {
            "name": "menu-management",
            "label": "菜单管理",
            "icon": "MenuOutlined",
            "path": "/menu-management",
            "component": "MenuManagement",
            "sort_order": 4,
            "permission": "admin"
        },
        {
            "name": "form-builder",
            "label": "表单构建",
            "icon": "FormOutlined",
            "path": "/form-builder",
            "component": "FormBuilder",
            "sort_order": 5,
            "permission": "admin"
        },
        {
            "name": "settings",
            "label": "系统设置",
            "icon": "SettingOutlined",
            "path": "/settings",
            "component": "Settings",
            "sort_order": 6,
            "permission": "admin"
        }
    ]

    for menu_data in default_menus:
        menu = Menu(**menu_data)
        db.add(menu)

    db.commit()
    print("Default menus created")

def init_db():
    # Create all tables
    Base.metadata.create_all(bind=engine)

    # Create default admin user if not exists
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin"),
                full_name="System Administrator",
                is_active=True,
                is_superuser=True
            )
            db.add(admin_user)
            db.commit()
            print("Default admin user created: admin/admin")

            # Initialize default menus
            init_default_menus(db)

            # Initialize demo data
            try:
                from app.db.demo_data import create_demo_data
                create_demo_data()
            except Exception as e:
                print(f"Warning: Could not initialize demo data: {e}")

            # Initialize sample data
            try:
                from app.db.sample_data import init_sample_data
                init_sample_data()
            except Exception as e:
                print(f"Warning: Could not initialize sample data: {e}")
    finally:
        db.close()
