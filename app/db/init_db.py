from sqlalchemy.orm import Session
from app.db.database import engine, SessionLocal, Base
from app.models.user import User
from app.models.customer import Customer
from app.models.contact import Contact
from app.models.opportunity import Opportunity
from app.models.custom_field import CustomField, CustomFieldValue
from app.core.security import get_password_hash

def init_db():
    # Create all tables
    Base.metadata.create_all(bind=engine)

    # Create default admin user if not exists
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                full_name="System Administrator",
                is_active=True,
                is_superuser=True
            )
            db.add(admin_user)
            db.commit()
            print("Default admin user created: admin/admin123")
    finally:
        db.close()
