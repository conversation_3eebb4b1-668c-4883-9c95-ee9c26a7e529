from sqlalchemy.orm import Session
from app.db.database import SessionLocal
from app.models.custom_field import CustomField, FieldType
from app.models.customer import Customer
from app.models.custom_field import CustomFieldValue
import json

def create_sample_custom_fields():
    """创建示例自定义字段"""
    db = SessionLocal()
    try:
        # 检查是否已经有自定义字段
        existing_fields = db.query(CustomField).filter(CustomField.entity_type == "customer").count()
        if existing_fields > 0:
            print("Custom fields already exist, skipping creation")
            return

        # 创建示例自定义字段
        sample_fields = [
            {
                "name": "customer_level",
                "label": "客户等级",
                "field_type": FieldType.SELECT,
                "entity_type": "customer",
                "options": json.dumps(["VIP客户", "重要客户", "普通客户", "潜在客户"]),
                "is_required": "false",
                "order_index": 1
            },
            {
                "name": "industry",
                "label": "所属行业",
                "field_type": FieldType.SELECT,
                "entity_type": "customer",
                "options": json.dumps(["互联网", "金融", "制造业", "教育", "医疗", "零售", "其他"]),
                "is_required": "false",
                "order_index": 2
            },
            {
                "name": "annual_revenue",
                "label": "年营收(万元)",
                "field_type": FieldType.NUMBER,
                "entity_type": "customer",
                "is_required": "false",
                "order_index": 3
            },
            {
                "name": "employee_count",
                "label": "员工人数",
                "field_type": FieldType.NUMBER,
                "entity_type": "customer",
                "is_required": "false",
                "order_index": 4
            },
            {
                "name": "website",
                "label": "公司网站",
                "field_type": FieldType.TEXT,
                "entity_type": "customer",
                "is_required": "false",
                "order_index": 5
            },
            {
                "name": "description",
                "label": "公司描述",
                "field_type": FieldType.TEXTAREA,
                "entity_type": "customer",
                "is_required": "false",
                "order_index": 6
            }
        ]

        for field_data in sample_fields:
            field = CustomField(**field_data)
            db.add(field)

        db.commit()
        print(f"Created {len(sample_fields)} sample custom fields")

    except Exception as e:
        print(f"Error creating sample fields: {e}")
        db.rollback()
    finally:
        db.close()

def create_sample_customers():
    """创建示例客户数据"""
    db = SessionLocal()
    try:
        # 检查是否已经有客户数据
        existing_customers = db.query(Customer).count()
        if existing_customers > 0:
            print("Customers already exist, skipping creation")
            return

        # 获取自定义字段
        custom_fields = db.query(CustomField).filter(CustomField.entity_type == "customer").all()
        field_map = {field.name: field.id for field in custom_fields}

        # 创建示例客户
        sample_customers = [
            {
                "name": "张三",
                "email": "<EMAIL>",
                "phone": "13800138001",
                "company": "科技有限公司",
                "address": "北京市朝阳区科技园区",
                "notes": "重要客户，有长期合作意向",
                "created_by": 1,
                "custom_fields": {
                    "customer_level": "VIP客户",
                    "industry": "互联网",
                    "annual_revenue": "5000",
                    "employee_count": "200",
                    "website": "https://example.com"
                }
            },
            {
                "name": "李四",
                "email": "<EMAIL>",
                "phone": "13800138002",
                "company": "制造集团",
                "address": "上海市浦东新区工业园",
                "notes": "制造业客户，需要定制化解决方案",
                "created_by": 1,
                "custom_fields": {
                    "customer_level": "重要客户",
                    "industry": "制造业",
                    "annual_revenue": "10000",
                    "employee_count": "500",
                    "website": "https://manufacturing.com"
                }
            },
            {
                "name": "王五",
                "email": "<EMAIL>",
                "phone": "13800138003",
                "company": "金融投资公司",
                "address": "深圳市福田区金融中心",
                "notes": "金融行业客户，对安全性要求较高",
                "created_by": 1,
                "custom_fields": {
                    "customer_level": "VIP客户",
                    "industry": "金融",
                    "annual_revenue": "8000",
                    "employee_count": "150",
                    "website": "https://finance.com"
                }
            }
        ]

        for customer_data in sample_customers:
            custom_field_data = customer_data.pop("custom_fields", {})
            
            # 创建客户
            customer = Customer(**customer_data)
            db.add(customer)
            db.flush()  # 获取客户ID

            # 创建自定义字段值
            for field_name, value in custom_field_data.items():
                if field_name in field_map:
                    field_value = CustomFieldValue(
                        field_id=field_map[field_name],
                        entity_id=customer.id,
                        entity_type="customer",
                        value=str(value)
                    )
                    db.add(field_value)

        db.commit()
        print(f"Created {len(sample_customers)} sample customers")

    except Exception as e:
        print(f"Error creating sample customers: {e}")
        db.rollback()
    finally:
        db.close()

def init_sample_data():
    """初始化示例数据"""
    print("Initializing sample data...")
    create_sample_custom_fields()
    create_sample_customers()
    print("Sample data initialization completed!")

if __name__ == "__main__":
    init_sample_data()
