from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from datetime import datetime

class CustomerBase(BaseModel):
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    custom_fields: Optional[Dict[str, Any]] = {}

class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    custom_fields: Optional[Dict[str, Any]] = {}

class CustomerInDBBase(CustomerBase):
    id: int
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Customer(CustomerInDBBase):
    custom_fields: Optional[Dict[str, Any]] = {}

class CustomerList(BaseModel):
    customers: List[Customer]
    total: int
    page: int
    size: int
