from typing import Optional, List, Any
from pydantic import BaseModel
from datetime import datetime
from app.models.custom_field import FieldType

class CustomFieldBase(BaseModel):
    name: str
    label: str
    field_type: FieldType
    entity_type: str
    options: Optional[str] = None
    is_required: str = "false"
    default_value: Optional[str] = None
    order_index: int = 0

class CustomFieldCreate(CustomFieldBase):
    pass

class CustomFieldUpdate(BaseModel):
    name: Optional[str] = None
    label: Optional[str] = None
    field_type: Optional[FieldType] = None
    options: Optional[str] = None
    is_required: Optional[str] = None
    default_value: Optional[str] = None
    order_index: Optional[int] = None

class CustomField(CustomFieldBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True

class CustomFieldValueBase(BaseModel):
    field_id: int
    entity_id: int
    entity_type: str
    value: Optional[str] = None

class CustomFieldValueCreate(CustomFieldValueBase):
    pass

class CustomFieldValueUpdate(BaseModel):
    value: Optional[str] = None

class CustomFieldValue(CustomFieldValueBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True
