from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Numeric, Enum
from sqlalchemy.sql import func
import enum
from app.db.database import Base

class OpportunityStage(enum.Enum):
    LEAD = "lead"
    QUALIFIED = "qualified"
    PROPOSAL = "proposal"
    NEGOTIATION = "negotiation"
    CLOSED_WON = "closed_won"
    CLOSED_LOST = "closed_lost"

class Opportunity(Base):
    __tablename__ = "opportunities"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    value = Column(Numeric(10, 2))
    stage = Column(Enum(OpportunityStage), default=OpportunityStage.LEAD)
    probability = Column(Integer, default=0)  # 0-100
    expected_close_date = Column(DateTime)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
