from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class Menu(Base):
    __tablename__ = "menus"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # 菜单名称
    label = Column(String(100), nullable=False)  # 显示标签
    icon = Column(String(50))  # 图标
    path = Column(String(200))  # 路径
    component = Column(String(100))  # 组件类型
    parent_id = Column(Integer, ForeignKey("menus.id"), nullable=True)  # 父菜单ID
    sort_order = Column(Integer, default=0)  # 排序
    is_active = Column(Boolean, default=True)  # 是否启用
    is_visible = Column(Boolean, default=True)  # 是否可见
    permission = Column(String(100))  # 权限标识
    description = Column(Text)  # 描述
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    parent = relationship("Menu", remote_side=[id], back_populates="children")
    children = relationship("Menu", back_populates="parent")

class FormTemplate(Base):
    __tablename__ = "form_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # 表单名称
    title = Column(String(200), nullable=False)  # 表单标题
    description = Column(Text)  # 表单描述
    form_config = Column(Text, nullable=False)  # 表单配置JSON
    entity_type = Column(String(50), default="custom")  # 实体类型
    is_active = Column(Boolean, default=True)  # 是否启用
    created_by = Column(Integer, ForeignKey("users.id"))  # 创建者
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    creator = relationship("User", back_populates="form_templates")

class FormData(Base):
    __tablename__ = "form_data"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("form_templates.id"), nullable=False)
    data = Column(Text, nullable=False)  # 表单数据JSON
    submitted_by = Column(Integer, ForeignKey("users.id"))  # 提交者
    status = Column(String(20), default="submitted")  # 状态
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    template = relationship("FormTemplate")
    submitter = relationship("User")
