from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class Chart(Base):
    __tablename__ = "charts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)  # 图表名称
    title = Column(String(200), nullable=False)  # 图表标题
    description = Column(Text)  # 图表描述
    chart_type = Column(String(50), nullable=False)  # 图表类型: bar, line, pie, area
    data_source = Column(String(100), nullable=False)  # 数据源
    chart_config = Column(Text, nullable=False)  # 图表配置JSON
    x_field = Column(String(100))  # X轴字段
    y_field = Column(String(100))  # Y轴字段
    filters = Column(Text)  # 过滤条件JSON
    is_active = Column(Boolean, default=True)  # 是否启用
    is_public = Column(Boolean, default=False)  # 是否公开
    created_by = Column(Integer, ForeignKey("users.id"))  # 创建者
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    creator = relationship("User")

class Dashboard(Base):
    __tablename__ = "dashboards"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # 仪表板名称
    title = Column(String(200), nullable=False)  # 仪表板标题
    description = Column(Text)  # 仪表板描述
    layout_config = Column(Text, nullable=False)  # 布局配置JSON
    is_default = Column(Boolean, default=False)  # 是否默认仪表板
    is_active = Column(Boolean, default=True)  # 是否启用
    created_by = Column(Integer, ForeignKey("users.id"))  # 创建者
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    creator = relationship("User")

class DashboardChart(Base):
    __tablename__ = "dashboard_charts"

    id = Column(Integer, primary_key=True, index=True)
    dashboard_id = Column(Integer, ForeignKey("dashboards.id"), nullable=False)
    chart_id = Column(Integer, ForeignKey("charts.id"), nullable=False)
    position_x = Column(Integer, default=0)  # X位置
    position_y = Column(Integer, default=0)  # Y位置
    width = Column(Integer, default=6)  # 宽度
    height = Column(Integer, default=4)  # 高度
    sort_order = Column(Integer, default=0)  # 排序
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    dashboard = relationship("Dashboard")
    chart = relationship("Chart")
