from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from app.db.database import Base

class FieldType(enum.Enum):
    TEXT = "text"
    NUMBER = "number"
    EMAIL = "email"
    PHONE = "phone"
    DATE = "date"
    DATETIME = "datetime"
    BOOLEAN = "boolean"
    SELECT = "select"
    TEXTAREA = "textarea"

class CustomField(Base):
    __tablename__ = "custom_fields"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    label = Column(String(100), nullable=False)
    field_type = Column(Enum(FieldType), nullable=False)
    entity_type = Column(String(50), nullable=False)  # customer, contact, opportunity
    options = Column(Text)  # JSON string for select options
    is_required = Column(String(10), default="false")  # "true" or "false" as string
    default_value = Column(Text)
    order_index = Column(Integer, default=0)
    created_at = Column(DateTime, server_default=func.now())

class CustomFieldValue(Base):
    __tablename__ = "custom_field_values"

    id = Column(Integer, primary_key=True, index=True)
    field_id = Column(Integer, ForeignKey("custom_fields.id"), nullable=False)
    entity_id = Column(Integer, nullable=False)  # ID of customer/contact/opportunity
    entity_type = Column(String(50), nullable=False)  # customer, contact, opportunity
    value = Column(Text)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
