from fastapi import APIRouter
from app.api.api_v1.endpoints import auth, customers, custom_fields, settings, menus

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(custom_fields.router, prefix="/custom-fields", tags=["custom-fields"])
api_router.include_router(settings.router, prefix="/settings", tags=["settings"])
api_router.include_router(menus.router, prefix="/menus", tags=["menus"])
