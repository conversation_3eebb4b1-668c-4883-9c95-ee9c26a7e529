from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.crud import customer as crud_customer
from app.schemas.customer import Customer, CustomerCreate, CustomerUpdate, CustomerList
from app.models.user import User

router = APIRouter()

@router.get("/")
def read_customers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    try:
        customers = crud_customer.get_customers(db, skip=skip, limit=limit)
        total = crud_customer.get_customers_count(db)

        # Add custom fields to each customer
        customers_with_fields = []
        for customer in customers:
            try:
                customer_dict = crud_customer.get_customer_with_custom_fields(db, customer.id)
                customers_with_fields.append(customer_dict)
            except Exception as e:
                # 如果获取自定义字段失败，返回基本信息
                customer_dict = {
                    "id": customer.id,
                    "name": customer.name,
                    "email": customer.email,
                    "phone": customer.phone,
                    "company": customer.company,
                    "address": customer.address,
                    "notes": customer.notes,
                    "created_by": customer.created_by,
                    "created_at": customer.created_at,
                    "updated_at": customer.updated_at,
                    "custom_fields": {}
                }
                customers_with_fields.append(customer_dict)

        return {
            "customers": customers_with_fields,
            "total": total,
            "page": skip // limit + 1,
            "size": limit
        }
    except Exception as e:
        print(f"Error in read_customers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=Customer)
def create_customer(
    customer: CustomerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_customer = crud_customer.create_customer(db=db, customer=customer, user_id=current_user.id)
    return crud_customer.get_customer_with_custom_fields(db, db_customer.id)

@router.get("/{customer_id}", response_model=Customer)
def read_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    customer = crud_customer.get_customer_with_custom_fields(db, customer_id=customer_id)
    if customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    return customer

@router.put("/{customer_id}", response_model=Customer)
def update_customer(
    customer_id: int,
    customer: CustomerUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_customer = crud_customer.update_customer(db=db, customer_id=customer_id, customer_update=customer)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    return crud_customer.get_customer_with_custom_fields(db, customer_id)

@router.delete("/{customer_id}")
def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    success = crud_customer.delete_customer(db=db, customer_id=customer_id)
    if not success:
        raise HTTPException(status_code=404, detail="Customer not found")
    return {"message": "Customer deleted successfully"}
