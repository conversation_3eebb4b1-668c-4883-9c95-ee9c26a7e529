from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.models.user import User
from app.models.chart import Chart, Dashboard, DashboardChart
from app.models.customer import Customer
from app.models.custom_field import <PERSON><PERSON><PERSON>
from pydantic import BaseModel
import json
from datetime import datetime, timedelta

# Pydantic models
class ChartCreate(BaseModel):
    name: str
    title: str
    description: str = ""
    chart_type: str
    data_source: str
    x_field: str
    y_field: str
    chart_config: str = "{}"

class DashboardCreate(BaseModel):
    name: str
    title: str
    description: str = ""
    layout_config: str = "{}"

router = APIRouter()

class ChartCreate(BaseModel):
    name: str
    title: str
    description: Optional[str] = None
    chart_type: str  # bar, line, pie, area
    data_source: str
    x_field: str
    y_field: str
    filters: Optional[str] = None
    is_public: bool = False

class ChartUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    chart_type: Optional[str] = None
    data_source: Optional[str] = None
    x_field: Optional[str] = None
    y_field: Optional[str] = None
    filters: Optional[str] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None

class DashboardCreate(BaseModel):
    name: str
    title: str
    description: Optional[str] = None
    layout_config: str
    is_default: bool = False

@router.get("/")
def get_charts(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表列表"""
    charts = db.query(Chart).filter(Chart.is_active == True).all()

    result = []
    for chart in charts:
        result.append({
            "id": chart.id,
            "name": chart.name,
            "title": chart.title,
            "description": chart.description,
            "chart_type": chart.chart_type,
            "data_source": chart.data_source,
            "x_field": chart.x_field,
            "y_field": chart.y_field,
            "is_public": chart.is_public,
            "created_at": chart.created_at
        })

    return {"charts": result, "total": len(result)}

@router.post("/")
def create_chart(
    chart: ChartCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建图表"""
    # 检查名称是否已存在
    existing = db.query(Chart).filter(Chart.name == chart.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="图表名称已存在")

    # 生成图表配置
    chart_config = {
        "type": chart.chart_type,
        "dataSource": chart.data_source,
        "xField": chart.x_field,
        "yField": chart.y_field,
        "title": chart.title,
        "description": chart.description
    }

    db_chart = Chart(
        name=chart.name,
        title=chart.title,
        description=chart.description,
        chart_type=chart.chart_type,
        data_source=chart.data_source,
        chart_config=json.dumps(chart_config),
        x_field=chart.x_field,
        y_field=chart.y_field,
        filters=chart.filters,
        is_public=chart.is_public,
        created_by=current_user.id
    )

    db.add(db_chart)
    db.commit()
    db.refresh(db_chart)
    return db_chart

@router.get("/{chart_id}")
def get_chart(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表详情"""
    chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    return chart

@router.put("/{chart_id}")
def update_chart(
    chart_id: int,
    chart: ChartUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新图表"""
    db_chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not db_chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 检查权限
    if db_chart.created_by != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="无权限修改此图表")

    for field, value in chart.dict(exclude_unset=True).items():
        setattr(db_chart, field, value)

    # 更新图表配置
    if any([chart.chart_type, chart.data_source, chart.x_field, chart.y_field]):
        chart_config = json.loads(db_chart.chart_config)
        if chart.chart_type:
            chart_config["type"] = chart.chart_type
        if chart.data_source:
            chart_config["dataSource"] = chart.data_source
        if chart.x_field:
            chart_config["xField"] = chart.x_field
        if chart.y_field:
            chart_config["yField"] = chart.y_field
        db_chart.chart_config = json.dumps(chart_config)

    db.commit()
    db.refresh(db_chart)
    return db_chart

@router.delete("/{chart_id}")
def delete_chart(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除图表"""
    db_chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not db_chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 检查权限
    if db_chart.created_by != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="无权限删除此图表")

    db.delete(db_chart)
    db.commit()
    return {"message": "图表删除成功"}

@router.get("/{chart_id}/data")
def get_chart_data(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表数据"""
    chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 根据数据源生成数据
    if chart.data_source == "customers":
        return get_customer_chart_data(db, chart)
    elif chart.data_source == "revenue":
        return get_revenue_chart_data(db, chart)
    else:
        # 默认返回模拟数据
        mock_data = generate_mock_data(chart)
        return {"data": mock_data, "total": len(mock_data)}

def get_customer_chart_data(db: Session, chart: Chart):
    """获取客户相关图表数据"""
    from collections import defaultdict
    from datetime import datetime, timedelta
    import random

    customers = db.query(Customer).all()

    if chart.chart_type == "line" and chart.x_field == "month":
        # 按月统计客户数量
        monthly_data = defaultdict(int)

        for customer in customers:
            if customer.created_at:
                month_key = customer.created_at.strftime("%Y-%m")
                monthly_data[month_key] += 1

        # 生成过去6个月的数据
        data = []
        current_date = datetime.now()

        for i in range(6):
            month_date = current_date - timedelta(days=30 * i)
            month_key = month_date.strftime("%Y-%m")
            month_label = month_date.strftime("%m月")

            data.append({
                "month": month_label,
                "count": monthly_data.get(month_key, 0),
                "revenue": monthly_data.get(month_key, 0) * 15000  # 模拟收入
            })

        data.reverse()  # 按时间正序
        return {"data": data, "total": len(data)}

    elif chart.chart_type == "pie" and chart.x_field == "company_type":
        # 按公司类型统计
        company_types = defaultdict(int)

        for customer in customers:
            if customer.company:
                # 简单分类逻辑
                company = customer.company.lower()
                if any(keyword in company for keyword in ["科技", "技术", "网络", "互联网"]):
                    company_types["科技公司"] += 1
                elif any(keyword in company for keyword in ["金融", "银行", "投资"]):
                    company_types["金融公司"] += 1
                elif any(keyword in company for keyword in ["制造", "工厂", "生产"]):
                    company_types["制造业"] += 1
                else:
                    company_types["其他"] += 1

        data = []
        for company_type, count in company_types.items():
            data.append({
                "company_type": company_type,
                "count": count,
                "label": company_type,
                "value": count
            })

        return {"data": data, "total": len(data)}

    # 默认返回客户总数统计
    data = [{"label": "总客户数", "value": len(customers), "count": len(customers)}]
    return {"data": data, "total": len(data)}

def get_revenue_chart_data(db: Session, chart: Chart):
    """获取收入相关图表数据"""
    from collections import defaultdict
    from datetime import datetime, timedelta
    import random

    # 基于客户数量模拟收入数据
    customers = db.query(Customer).all()

    monthly_customers = defaultdict(int)
    for customer in customers:
        if customer.created_at:
            month_key = customer.created_at.strftime("%Y-%m")
            monthly_customers[month_key] += 1

    data = []
    current_date = datetime.now()

    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_key = month_date.strftime("%Y-%m")
        month_label = month_date.strftime("%m月")

        customer_count = monthly_customers.get(month_key, 0)
        # 基于客户数量计算模拟收入
        base_revenue = customer_count * 15000
        # 添加一些随机波动
        revenue = base_revenue + random.randint(-5000, 10000)

        data.append({
            "month": month_label,
            "amount": max(revenue, 0),
            "revenue": max(revenue, 0),
            "count": customer_count
        })

    data.reverse()  # 按时间正序
    return {"data": data, "total": len(data)}

def generate_mock_data(chart: Chart):
    """生成模拟数据"""
    import random
    from datetime import datetime, timedelta

    data = []
    current_date = datetime.now()

    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_label = month_date.strftime("%m月")

        data.append({
            "month": month_label,
            "count": random.randint(10, 50),
            "amount": random.randint(10000, 50000),
            "revenue": random.randint(15000, 35000),
            "value": random.randint(20, 100)
        })

    data.reverse()
    return data

@router.get("/fields/{data_source}")
def get_data_source_fields(
    data_source: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取数据源的可用字段"""
    try:
        if data_source == "customers":
            # 客户表字段
            fields = [
                {"name": "id", "label": "客户ID", "type": "number", "description": "客户唯一标识"},
                {"name": "name", "label": "客户姓名", "type": "text", "description": "客户的姓名"},
                {"name": "email", "label": "邮箱地址", "type": "text", "description": "客户的邮箱"},
                {"name": "phone", "label": "电话号码", "type": "text", "description": "客户的联系电话"},
                {"name": "company", "label": "公司名称", "type": "text", "description": "客户所在公司"},
                {"name": "address", "label": "地址", "type": "text", "description": "客户地址"},
                {"name": "created_at", "label": "创建时间", "type": "date", "description": "客户记录创建时间"},
                {"name": "updated_at", "label": "更新时间", "type": "date", "description": "客户记录最后更新时间"},
                # 聚合字段
                {"name": "month", "label": "月份", "type": "text", "description": "按月份分组"},
                {"name": "quarter", "label": "季度", "type": "text", "description": "按季度分组"},
                {"name": "year", "label": "年份", "type": "text", "description": "按年份分组"},
                {"name": "count", "label": "客户数量", "type": "number", "description": "客户总数"},
                {"name": "company_type", "label": "公司类型", "type": "text", "description": "公司类型分类"}
            ]

            # 添加自定义字段
            custom_fields = db.query(CustomField).filter(
                CustomField.entity_type == "customer"
            ).all()

            for field in custom_fields:
                fields.append({
                    "name": field.name,
                    "label": field.label,
                    "type": "text" if field.field_type.value == "text" else field.field_type.value,
                    "description": f"自定义字段: {field.label}"
                })

        elif data_source == "revenue":
            fields = [
                {"name": "month", "label": "月份", "type": "text", "description": "收入月份"},
                {"name": "quarter", "label": "季度", "type": "text", "description": "收入季度"},
                {"name": "year", "label": "年份", "type": "text", "description": "收入年份"},
                {"name": "amount", "label": "收入金额", "type": "number", "description": "收入总金额"},
                {"name": "revenue", "label": "营收", "type": "number", "description": "营业收入"},
                {"name": "profit", "label": "利润", "type": "number", "description": "净利润"},
                {"name": "customer_count", "label": "客户数", "type": "number", "description": "产生收入的客户数量"}
            ]

        elif data_source == "orders":
            fields = [
                {"name": "id", "label": "订单ID", "type": "number", "description": "订单唯一标识"},
                {"name": "customer_id", "label": "客户ID", "type": "number", "description": "关联的客户ID"},
                {"name": "amount", "label": "订单金额", "type": "number", "description": "订单总金额"},
                {"name": "status", "label": "订单状态", "type": "text", "description": "订单当前状态"},
                {"name": "created_at", "label": "创建时间", "type": "date", "description": "订单创建时间"},
                {"name": "month", "label": "月份", "type": "text", "description": "按月份分组"},
                {"name": "count", "label": "订单数量", "type": "number", "description": "订单总数"}
            ]

        else:
            fields = [
                {"name": "category", "label": "分类", "type": "text", "description": "数据分类"},
                {"name": "value", "label": "数值", "type": "number", "description": "数据值"},
                {"name": "count", "label": "数量", "type": "number", "description": "计数"},
                {"name": "date", "label": "日期", "type": "date", "description": "日期字段"}
            ]

        return {"fields": fields, "total": len(fields)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取字段失败: {str(e)}")

@router.get("/data-sources")
def get_data_sources(
    current_user: User = Depends(get_current_active_user)
):
    """获取可用的数据源列表"""
    data_sources = [
        {
            "name": "customers",
            "label": "客户数据",
            "description": "客户信息和相关统计数据",
            "icon": "👥",
            "fields_count": 10
        },
        {
            "name": "revenue",
            "label": "收入数据",
            "description": "收入、营收和利润相关数据",
            "icon": "💰",
            "fields_count": 7
        },
        {
            "name": "orders",
            "label": "订单数据",
            "description": "订单信息和交易数据",
            "icon": "📦",
            "fields_count": 6
        },
        {
            "name": "custom",
            "label": "自定义数据",
            "description": "用户自定义的数据源",
            "icon": "🔧",
            "fields_count": 4
        }
    ]

    return {"data_sources": data_sources, "total": len(data_sources)}

@router.get("/stats")
def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取仪表板统计数据"""
    try:
        # 获取客户统计
        total_customers = db.query(Customer).count()

        # 获取本月新增客户
        from datetime import datetime, timedelta
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_customers_this_month = db.query(Customer).filter(
            Customer.created_at >= current_month_start
        ).count()

        # 模拟其他统计数据
        stats = {
            "total_customers": total_customers,
            "new_customers": new_customers_this_month,
            "total_revenue": 88888,  # 模拟数据
            "active_deals": 15,      # 模拟数据
            "conversion_rate": 23.5, # 模拟数据
            "avg_deal_size": 12500   # 模拟数据
        }

        return {"stats": stats}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

@router.get("/stats/available")
def get_available_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取可用的统计指标"""
    available_stats = [
        {
            "key": "total_customers",
            "label": "总客户数",
            "description": "系统中的客户总数",
            "icon": "👥",
            "type": "count",
            "color": "#1890ff"
        },
        {
            "key": "new_customers",
            "label": "本月新增客户",
            "description": "本月新注册的客户数量",
            "icon": "🆕",
            "type": "count",
            "color": "#52c41a"
        },
        {
            "key": "total_revenue",
            "label": "总收入",
            "description": "累计收入金额",
            "icon": "💰",
            "type": "currency",
            "color": "#faad14"
        },
        {
            "key": "active_deals",
            "label": "活跃交易",
            "description": "正在进行的交易数量",
            "icon": "🤝",
            "type": "count",
            "color": "#722ed1"
        },
        {
            "key": "conversion_rate",
            "label": "转化率",
            "description": "线索到客户的转化率",
            "icon": "📈",
            "type": "percentage",
            "color": "#eb2f96"
        },
        {
            "key": "avg_deal_size",
            "label": "平均交易额",
            "description": "平均每笔交易的金额",
            "icon": "💵",
            "type": "currency",
            "color": "#13c2c2"
        }
    ]

    return {"stats": available_stats, "total": len(available_stats)}

@router.get("/entity-fields/{entity_type}")
def get_entity_fields(
    entity_type: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取实体类型的可用字段（用于表单构建器）"""
    try:
        if entity_type == "customer":
            # 客户实体的基础字段
            base_fields = [
                {"name": "name", "label": "客户姓名", "type": "text", "required": True, "description": "客户的姓名"},
                {"name": "email", "label": "邮箱地址", "type": "email", "required": False, "description": "客户的邮箱"},
                {"name": "phone", "label": "电话号码", "type": "phone", "required": False, "description": "客户的联系电话"},
                {"name": "company", "label": "公司名称", "type": "text", "required": False, "description": "客户所在公司"},
                {"name": "address", "label": "地址", "type": "textarea", "required": False, "description": "客户地址"},
                {"name": "notes", "label": "备注", "type": "textarea", "required": False, "description": "客户备注信息"}
            ]

            # 获取自定义字段
            custom_fields = db.query(CustomField).filter(
                CustomField.entity_type == "customer"
            ).all()

            for field in custom_fields:
                base_fields.append({
                    "name": field.name,
                    "label": field.label,
                    "type": field.field_type.value,
                    "required": field.is_required == "true",
                    "description": f"自定义字段: {field.label}",
                    "options": json.loads(field.options) if field.options else None
                })

        elif entity_type == "order":
            base_fields = [
                {"name": "customer_id", "label": "客户ID", "type": "number", "required": True, "description": "关联的客户"},
                {"name": "amount", "label": "订单金额", "type": "number", "required": True, "description": "订单总金额"},
                {"name": "status", "label": "订单状态", "type": "select", "required": True, "description": "订单当前状态",
                 "options": ["待付款", "已付款", "已发货", "已完成", "已取消"]},
                {"name": "product_name", "label": "产品名称", "type": "text", "required": True, "description": "订购的产品"},
                {"name": "quantity", "label": "数量", "type": "number", "required": True, "description": "订购数量"},
                {"name": "delivery_address", "label": "收货地址", "type": "textarea", "required": False, "description": "收货地址"},
                {"name": "notes", "label": "订单备注", "type": "textarea", "required": False, "description": "订单备注"}
            ]

        elif entity_type == "lead":
            base_fields = [
                {"name": "lead_name", "label": "线索名称", "type": "text", "required": True, "description": "销售线索名称"},
                {"name": "company", "label": "公司名称", "type": "text", "required": True, "description": "潜在客户公司"},
                {"name": "contact_person", "label": "联系人", "type": "text", "required": True, "description": "主要联系人"},
                {"name": "contact_phone", "label": "联系电话", "type": "phone", "required": True, "description": "联系电话"},
                {"name": "contact_email", "label": "联系邮箱", "type": "email", "required": False, "description": "联系邮箱"},
                {"name": "lead_source", "label": "线索来源", "type": "select", "required": True, "description": "线索来源渠道",
                 "options": ["网站咨询", "电话咨询", "展会", "推荐", "广告", "其他"]},
                {"name": "budget", "label": "预算范围", "type": "select", "required": False, "description": "客户预算",
                 "options": ["10万以下", "10-50万", "50-100万", "100万以上"]},
                {"name": "priority", "label": "优先级", "type": "select", "required": False, "description": "线索优先级",
                 "options": ["高", "中", "低"]},
                {"name": "description", "label": "需求描述", "type": "textarea", "required": False, "description": "客户需求描述"}
            ]

        else:
            # 自定义实体的通用字段
            base_fields = [
                {"name": "title", "label": "标题", "type": "text", "required": True, "description": "记录标题"},
                {"name": "description", "label": "描述", "type": "textarea", "required": False, "description": "详细描述"},
                {"name": "status", "label": "状态", "type": "select", "required": False, "description": "记录状态",
                 "options": ["草稿", "进行中", "已完成", "已取消"]},
                {"name": "priority", "label": "优先级", "type": "select", "required": False, "description": "优先级",
                 "options": ["高", "中", "低"]},
                {"name": "tags", "label": "标签", "type": "text", "required": False, "description": "标签（逗号分隔）"}
            ]

        return {"fields": base_fields, "total": len(base_fields)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体字段失败: {str(e)}")

# 仪表板管理
@router.get("/dashboards/")
def get_dashboards(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取仪表板列表"""
    dashboards = db.query(Dashboard).filter(Dashboard.is_active == True).all()
    return {"dashboards": dashboards, "total": len(dashboards)}

@router.post("/dashboards/")
def create_dashboard(
    dashboard: DashboardCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建仪表板"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    db_dashboard = Dashboard(
        **dashboard.dict(),
        created_by=current_user.id
    )

    db.add(db_dashboard)
    db.commit()
    db.refresh(db_dashboard)
    return db_dashboard
