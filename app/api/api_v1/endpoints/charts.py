from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.models.user import User
from app.models.chart import Chart, Dashboard, DashboardChart
from app.models.customer import Customer
from pydantic import BaseModel
import json
from datetime import datetime, timedelta

router = APIRouter()

class ChartCreate(BaseModel):
    name: str
    title: str
    description: Optional[str] = None
    chart_type: str  # bar, line, pie, area
    data_source: str
    x_field: str
    y_field: str
    filters: Optional[str] = None
    is_public: bool = False

class ChartUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    chart_type: Optional[str] = None
    data_source: Optional[str] = None
    x_field: Optional[str] = None
    y_field: Optional[str] = None
    filters: Optional[str] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None

class DashboardCreate(BaseModel):
    name: str
    title: str
    description: Optional[str] = None
    layout_config: str
    is_default: bool = False

@router.get("/")
def get_charts(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表列表"""
    charts = db.query(Chart).filter(Chart.is_active == True).all()

    result = []
    for chart in charts:
        result.append({
            "id": chart.id,
            "name": chart.name,
            "title": chart.title,
            "description": chart.description,
            "chart_type": chart.chart_type,
            "data_source": chart.data_source,
            "x_field": chart.x_field,
            "y_field": chart.y_field,
            "is_public": chart.is_public,
            "created_at": chart.created_at
        })

    return {"charts": result, "total": len(result)}

@router.post("/")
def create_chart(
    chart: ChartCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建图表"""
    # 检查名称是否已存在
    existing = db.query(Chart).filter(Chart.name == chart.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="图表名称已存在")

    # 生成图表配置
    chart_config = {
        "type": chart.chart_type,
        "dataSource": chart.data_source,
        "xField": chart.x_field,
        "yField": chart.y_field,
        "title": chart.title,
        "description": chart.description
    }

    db_chart = Chart(
        name=chart.name,
        title=chart.title,
        description=chart.description,
        chart_type=chart.chart_type,
        data_source=chart.data_source,
        chart_config=json.dumps(chart_config),
        x_field=chart.x_field,
        y_field=chart.y_field,
        filters=chart.filters,
        is_public=chart.is_public,
        created_by=current_user.id
    )

    db.add(db_chart)
    db.commit()
    db.refresh(db_chart)
    return db_chart

@router.get("/{chart_id}")
def get_chart(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表详情"""
    chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    return chart

@router.put("/{chart_id}")
def update_chart(
    chart_id: int,
    chart: ChartUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新图表"""
    db_chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not db_chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 检查权限
    if db_chart.created_by != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="无权限修改此图表")

    for field, value in chart.dict(exclude_unset=True).items():
        setattr(db_chart, field, value)

    # 更新图表配置
    if any([chart.chart_type, chart.data_source, chart.x_field, chart.y_field]):
        chart_config = json.loads(db_chart.chart_config)
        if chart.chart_type:
            chart_config["type"] = chart.chart_type
        if chart.data_source:
            chart_config["dataSource"] = chart.data_source
        if chart.x_field:
            chart_config["xField"] = chart.x_field
        if chart.y_field:
            chart_config["yField"] = chart.y_field
        db_chart.chart_config = json.dumps(chart_config)

    db.commit()
    db.refresh(db_chart)
    return db_chart

@router.delete("/{chart_id}")
def delete_chart(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除图表"""
    db_chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not db_chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 检查权限
    if db_chart.created_by != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="无权限删除此图表")

    db.delete(db_chart)
    db.commit()
    return {"message": "图表删除成功"}

@router.get("/{chart_id}/data")
def get_chart_data(
    chart_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表数据"""
    chart = db.query(Chart).filter(Chart.id == chart_id).first()
    if not chart:
        raise HTTPException(status_code=404, detail="图表不存在")

    # 根据数据源生成数据
    if chart.data_source == "customers":
        return get_customer_chart_data(db, chart)
    elif chart.data_source == "revenue":
        return get_revenue_chart_data(db, chart)
    else:
        # 默认返回模拟数据
        mock_data = generate_mock_data(chart)
        return {"data": mock_data, "total": len(mock_data)}

def get_customer_chart_data(db: Session, chart: Chart):
    """获取客户相关图表数据"""
    from collections import defaultdict
    from datetime import datetime, timedelta
    import random

    customers = db.query(Customer).all()

    if chart.chart_type == "line" and chart.x_field == "month":
        # 按月统计客户数量
        monthly_data = defaultdict(int)

        for customer in customers:
            if customer.created_at:
                month_key = customer.created_at.strftime("%Y-%m")
                monthly_data[month_key] += 1

        # 生成过去6个月的数据
        data = []
        current_date = datetime.now()

        for i in range(6):
            month_date = current_date - timedelta(days=30 * i)
            month_key = month_date.strftime("%Y-%m")
            month_label = month_date.strftime("%m月")

            data.append({
                "month": month_label,
                "count": monthly_data.get(month_key, 0),
                "revenue": monthly_data.get(month_key, 0) * 15000  # 模拟收入
            })

        data.reverse()  # 按时间正序
        return {"data": data, "total": len(data)}

    elif chart.chart_type == "pie" and chart.x_field == "company_type":
        # 按公司类型统计
        company_types = defaultdict(int)

        for customer in customers:
            if customer.company:
                # 简单分类逻辑
                company = customer.company.lower()
                if any(keyword in company for keyword in ["科技", "技术", "网络", "互联网"]):
                    company_types["科技公司"] += 1
                elif any(keyword in company for keyword in ["金融", "银行", "投资"]):
                    company_types["金融公司"] += 1
                elif any(keyword in company for keyword in ["制造", "工厂", "生产"]):
                    company_types["制造业"] += 1
                else:
                    company_types["其他"] += 1

        data = []
        for company_type, count in company_types.items():
            data.append({
                "company_type": company_type,
                "count": count,
                "label": company_type,
                "value": count
            })

        return {"data": data, "total": len(data)}

    # 默认返回客户总数统计
    data = [{"label": "总客户数", "value": len(customers), "count": len(customers)}]
    return {"data": data, "total": len(data)}

def get_revenue_chart_data(db: Session, chart: Chart):
    """获取收入相关图表数据"""
    from collections import defaultdict
    from datetime import datetime, timedelta
    import random

    # 基于客户数量模拟收入数据
    customers = db.query(Customer).all()

    monthly_customers = defaultdict(int)
    for customer in customers:
        if customer.created_at:
            month_key = customer.created_at.strftime("%Y-%m")
            monthly_customers[month_key] += 1

    data = []
    current_date = datetime.now()

    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_key = month_date.strftime("%Y-%m")
        month_label = month_date.strftime("%m月")

        customer_count = monthly_customers.get(month_key, 0)
        # 基于客户数量计算模拟收入
        base_revenue = customer_count * 15000
        # 添加一些随机波动
        revenue = base_revenue + random.randint(-5000, 10000)

        data.append({
            "month": month_label,
            "amount": max(revenue, 0),
            "revenue": max(revenue, 0),
            "count": customer_count
        })

    data.reverse()  # 按时间正序
    return {"data": data, "total": len(data)}

def generate_mock_data(chart: Chart):
    """生成模拟数据"""
    import random
    from datetime import datetime, timedelta

    data = []
    current_date = datetime.now()

    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_label = month_date.strftime("%m月")

        data.append({
            "month": month_label,
            "count": random.randint(10, 50),
            "amount": random.randint(10000, 50000),
            "revenue": random.randint(15000, 35000),
            "value": random.randint(20, 100)
        })

    data.reverse()
    return data

# 仪表板管理
@router.get("/dashboards/")
def get_dashboards(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取仪表板列表"""
    dashboards = db.query(Dashboard).filter(Dashboard.is_active == True).all()
    return {"dashboards": dashboards, "total": len(dashboards)}

@router.post("/dashboards/")
def create_dashboard(
    dashboard: DashboardCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建仪表板"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    db_dashboard = Dashboard(
        **dashboard.dict(),
        created_by=current_user.id
    )

    db.add(db_dashboard)
    db.commit()
    db.refresh(db_dashboard)
    return db_dashboard
