from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.models.user import User
from app.core.security import get_password_hash, verify_password
from pydantic import BaseModel
from typing import Optional

router = APIRouter()

class DatabaseSettings(BaseModel):
    db_type: str = "sqlite"  # sqlite, mysql
    db_host: Optional[str] = None
    db_port: Optional[int] = None
    db_name: Optional[str] = None
    db_user: Optional[str] = None
    db_password: Optional[str] = None

class UserProfileUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None

class PasswordChange(BaseModel):
    current_password: str
    new_password: str

class SystemSettings(BaseModel):
    system_name: str = "PyCRM"
    system_version: str = "1.0.0"
    max_customers: int = 10000
    backup_enabled: bool = True

@router.get("/profile")
def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """获取当前用户资料"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "created_at": current_user.created_at
    }

@router.put("/profile")
def update_user_profile(
    profile_update: UserProfileUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新用户资料"""
    try:
        if profile_update.username:
            # 检查用户名是否已存在
            existing_user = db.query(User).filter(
                User.username == profile_update.username,
                User.id != current_user.id
            ).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="用户名已存在")
            current_user.username = profile_update.username
        
        if profile_update.email:
            current_user.email = profile_update.email
        
        if profile_update.full_name:
            current_user.full_name = profile_update.full_name
        
        db.commit()
        db.refresh(current_user)
        
        return {
            "message": "资料更新成功",
            "user": {
                "id": current_user.id,
                "username": current_user.username,
                "email": current_user.email,
                "full_name": current_user.full_name
            }
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/change-password")
def change_password(
    password_change: PasswordChange,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """修改密码"""
    # 验证当前密码
    if not verify_password(password_change.current_password, current_user.hashed_password):
        raise HTTPException(status_code=400, detail="当前密码错误")
    
    # 更新密码
    current_user.hashed_password = get_password_hash(password_change.new_password)
    db.commit()
    
    return {"message": "密码修改成功"}

@router.get("/database")
def get_database_settings(
    current_user: User = Depends(get_current_active_user)
):
    """获取数据库设置"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 这里返回当前数据库配置信息
    return {
        "db_type": "sqlite",
        "db_name": "crm.db",
        "status": "connected",
        "tables_count": 4,
        "last_backup": None
    }

@router.post("/database/test-connection")
def test_database_connection(
    db_settings: DatabaseSettings,
    current_user: User = Depends(get_current_active_user)
):
    """测试数据库连接"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        if db_settings.db_type == "mysql":
            # 这里可以添加MySQL连接测试逻辑
            # import pymysql
            # connection = pymysql.connect(
            #     host=db_settings.db_host,
            #     port=db_settings.db_port,
            #     user=db_settings.db_user,
            #     password=db_settings.db_password,
            #     database=db_settings.db_name
            # )
            # connection.close()
            return {"status": "success", "message": "MySQL连接测试成功"}
        else:
            return {"status": "success", "message": "SQLite连接正常"}
    except Exception as e:
        return {"status": "error", "message": f"连接失败: {str(e)}"}

@router.get("/system")
def get_system_settings(
    current_user: User = Depends(get_current_active_user)
):
    """获取系统设置"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    return {
        "system_name": "PyCRM",
        "system_version": "1.0.0",
        "database_type": "SQLite",
        "total_users": 1,
        "total_customers": 0,
        "total_custom_fields": 0,
        "uptime": "运行中",
        "last_backup": None
    }

@router.post("/system/backup")
def create_backup(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建数据备份"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        import shutil
        import datetime
        
        # 创建备份文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"crm_backup_{timestamp}.db"
        
        # 复制数据库文件
        shutil.copy2("crm.db", f"backups/{backup_filename}")
        
        return {
            "status": "success",
            "message": "备份创建成功",
            "backup_file": backup_filename,
            "created_at": timestamp
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"备份失败: {str(e)}"
        }
