from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.crud import custom_field as crud_custom_field
from app.schemas.custom_field import CustomField, CustomFieldCreate, CustomFieldUpdate
from app.models.user import User

router = APIRouter()

@router.get("/", response_model=dict)
def read_custom_fields(
    entity_type: str = "customer",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    fields = crud_custom_field.get_custom_fields_by_entity(db, entity_type=entity_type)
    return {"fields": fields, "total": len(fields)}

@router.post("/", response_model=CustomField)
def create_custom_field(
    field: CustomFieldCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    return crud_custom_field.create_custom_field(db=db, field=field)

@router.get("/{field_id}", response_model=CustomField)
def read_custom_field(
    field_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    field = crud_custom_field.get_custom_field(db, field_id=field_id)
    if field is None:
        raise HTTPException(status_code=404, detail="Custom field not found")
    return field

@router.put("/{field_id}", response_model=CustomField)
def update_custom_field(
    field_id: int,
    field: CustomFieldUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    db_field = crud_custom_field.update_custom_field(db=db, field_id=field_id, field_update=field)
    if db_field is None:
        raise HTTPException(status_code=404, detail="Custom field not found")
    return db_field

@router.delete("/{field_id}")
def delete_custom_field(
    field_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    success = crud_custom_field.delete_custom_field(db=db, field_id=field_id)
    if not success:
        raise HTTPException(status_code=404, detail="Custom field not found")
    return {"message": "Custom field deleted successfully"}
