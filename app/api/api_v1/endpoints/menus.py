from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.api.deps import get_current_active_user
from app.models.user import User
from app.models.menu import Menu, FormTemplate, FormData
from pydantic import BaseModel
import json

router = APIRouter()

class MenuCreate(BaseModel):
    name: str
    label: str
    icon: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: int = 0
    is_active: bool = True
    is_visible: bool = True
    permission: Optional[str] = None
    description: Optional[str] = None

class MenuUpdate(BaseModel):
    name: Optional[str] = None
    label: Optional[str] = None
    icon: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None
    is_visible: Optional[bool] = None
    permission: Optional[str] = None
    description: Optional[str] = None

class FormTemplateCreate(BaseModel):
    name: str
    title: str
    description: Optional[str] = None
    form_config: str  # JSON string
    entity_type: str = "custom"

class FormTemplateUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    form_config: Optional[str] = None
    entity_type: Optional[str] = None
    is_active: Optional[bool] = None

class FormDataSubmit(BaseModel):
    template_id: int
    data: dict

@router.get("/")
def get_menus(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取菜单列表"""
    menus = db.query(Menu).filter(Menu.is_active == True).order_by(Menu.sort_order).all()
    
    # 构建树形结构
    menu_dict = {}
    root_menus = []
    
    for menu in menus:
        menu_dict[menu.id] = {
            "id": menu.id,
            "name": menu.name,
            "label": menu.label,
            "icon": menu.icon,
            "path": menu.path,
            "component": menu.component,
            "parent_id": menu.parent_id,
            "sort_order": menu.sort_order,
            "is_active": menu.is_active,
            "is_visible": menu.is_visible,
            "permission": menu.permission,
            "description": menu.description,
            "children": []
        }
    
    for menu in menus:
        if menu.parent_id is None:
            root_menus.append(menu_dict[menu.id])
        else:
            if menu.parent_id in menu_dict:
                menu_dict[menu.parent_id]["children"].append(menu_dict[menu.id])
    
    return {"menus": root_menus, "total": len(menus)}

@router.post("/")
def create_menu(
    menu: MenuCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建菜单"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    db_menu = Menu(**menu.dict())
    db.add(db_menu)
    db.commit()
    db.refresh(db_menu)
    return db_menu

@router.put("/{menu_id}")
def update_menu(
    menu_id: int,
    menu: MenuUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新菜单"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    db_menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not db_menu:
        raise HTTPException(status_code=404, detail="菜单不存在")
    
    for field, value in menu.dict(exclude_unset=True).items():
        setattr(db_menu, field, value)
    
    db.commit()
    db.refresh(db_menu)
    return db_menu

@router.delete("/{menu_id}")
def delete_menu(
    menu_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除菜单"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    db_menu = db.query(Menu).filter(Menu.id == menu_id).first()
    if not db_menu:
        raise HTTPException(status_code=404, detail="菜单不存在")
    
    # 检查是否有子菜单
    children = db.query(Menu).filter(Menu.parent_id == menu_id).count()
    if children > 0:
        raise HTTPException(status_code=400, detail="请先删除子菜单")
    
    db.delete(db_menu)
    db.commit()
    return {"message": "菜单删除成功"}

# 表单模板管理
@router.get("/forms")
def get_form_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取表单模板列表"""
    templates = db.query(FormTemplate).filter(FormTemplate.is_active == True).all()
    return {"templates": templates, "total": len(templates)}

@router.post("/forms")
def create_form_template(
    template: FormTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建表单模板"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 验证JSON格式
    try:
        json.loads(template.form_config)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="表单配置格式错误")
    
    db_template = FormTemplate(
        **template.dict(),
        created_by=current_user.id
    )
    db.add(db_template)
    db.commit()
    db.refresh(db_template)
    return db_template

@router.put("/forms/{template_id}")
def update_form_template(
    template_id: int,
    template: FormTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新表单模板"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    db_template = db.query(FormTemplate).filter(FormTemplate.id == template_id).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="表单模板不存在")
    
    # 验证JSON格式
    if template.form_config:
        try:
            json.loads(template.form_config)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="表单配置格式错误")
    
    for field, value in template.dict(exclude_unset=True).items():
        setattr(db_template, field, value)
    
    db.commit()
    db.refresh(db_template)
    return db_template

@router.delete("/forms/{template_id}")
def delete_form_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除表单模板"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    db_template = db.query(FormTemplate).filter(FormTemplate.id == template_id).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="表单模板不存在")
    
    db.delete(db_template)
    db.commit()
    return {"message": "表单模板删除成功"}

@router.post("/forms/{template_id}/submit")
def submit_form_data(
    template_id: int,
    form_data: FormDataSubmit,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """提交表单数据"""
    template = db.query(FormTemplate).filter(FormTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="表单模板不存在")
    
    db_form_data = FormData(
        template_id=template_id,
        data=json.dumps(form_data.data),
        submitted_by=current_user.id
    )
    db.add(db_form_data)
    db.commit()
    db.refresh(db_form_data)
    return {"message": "表单提交成功", "id": db_form_data.id}

@router.get("/forms/{template_id}/data")
def get_form_data(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取表单数据"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    form_data = db.query(FormData).filter(FormData.template_id == template_id).all()
    
    result = []
    for data in form_data:
        result.append({
            "id": data.id,
            "data": json.loads(data.data),
            "submitted_by": data.submitted_by,
            "status": data.status,
            "created_at": data.created_at
        })
    
    return {"data": result, "total": len(result)}
