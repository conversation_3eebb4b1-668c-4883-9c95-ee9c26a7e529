#!/usr/bin/env python3

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Starting PyCRM server...")

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    print("✓ FastAPI imported")

    # Simple app without complex imports first
    app = FastAPI(title="PyCRM", description="Customer Relationship Management System")

    # Add CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app.get("/")
    async def root():
        return {"message": "Welcome to PyCRM API", "status": "running"}

    @app.get("/health")
    async def health():
        return {"status": "healthy"}

    print("✓ App created successfully")

    # Start server
    import uvicorn
    print("Starting server on http://0.0.0.0:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
