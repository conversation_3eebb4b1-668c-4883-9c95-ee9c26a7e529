#!/usr/bin/env python3

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Starting PyCRM server...")

try:
    # Import the main app
    from main import app
    print("✓ Main app imported successfully")

    # Start server
    import uvicorn
    print("Starting server on http://0.0.0.0:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
