#!/usr/bin/env python3

import sqlite3
from passlib.context import CryptContext

# 创建密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def fix_admin_password():
    # 连接数据库
    conn = sqlite3.connect('crm.db')
    cursor = conn.cursor()
    
    try:
        # 生成新的密码哈希
        new_password_hash = hash_password('admin')
        print(f"New password hash: {new_password_hash}")
        
        # 更新admin用户的密码
        cursor.execute(
            "UPDATE users SET hashed_password = ? WHERE username = 'admin'",
            (new_password_hash,)
        )
        
        # 检查是否更新成功
        if cursor.rowcount > 0:
            conn.commit()
            print("✅ Admin password updated successfully!")
            print("Username: admin")
            print("Password: admin")
        else:
            print("❌ Admin user not found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_admin_password()
