from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.core.config import settings
from app.api.api_v1.api import api_router
from app.db.init_db import init_db
import os

app = FastAPI(
    title=settings.APP_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Mount static files
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

@app.on_event("startup")
async def startup_event():
    init_db()

@app.get("/")
async def root():
    # 如果存在简单版本，返回simple.html，否则返回API信息
    if os.path.exists("static/simple.html"):
        return FileResponse("static/simple.html")
    elif os.path.exists("static/index.html"):
        return FileResponse("static/index.html")
    return {"message": "Welcome to PyCRM API", "status": "running"}

@app.get("/api")
async def api_info():
    return {"message": "Welcome to PyCRM API", "status": "running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
